var q=function(G){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(Y){return typeof Y}:function(Y){return Y&&typeof Symbol=="function"&&Y.constructor===Symbol&&Y!==Symbol.prototype?"symbol":typeof Y},q(G)},$=function(G,Y){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);Y&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(G,N).enumerable})),J.push.apply(J,Z)}return J},K=function(G){for(var Y=1;Y<arguments.length;Y++){var J=arguments[Y]!=null?arguments[Y]:{};Y%2?$(Object(J),!0).forEach(function(Z){E0(G,Z,J[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):$(Object(J)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(J,Z))})}return G},E0=function(G,Y,J){if(Y=B0(Y),Y in G)Object.defineProperty(G,Y,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[Y]=J;return G},B0=function(G){var Y=C0(G,"string");return q(Y)=="symbol"?Y:String(Y)},C0=function(G,Y){if(q(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var Z=J.call(G,Y||"default");if(q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Y==="string"?String:Number)(G)};(function(G){var Y=Object.defineProperty,J=function E(B,C){for(var U in C)Y(B,U,{get:C[U],enumerable:!0,configurable:!0,set:function H(X){return C[U]=function(){return X}}})},Z={lessThanXSeconds:{one:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 1 \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35",other:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 {{count}} \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"},xSeconds:{one:"1 \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35",other:"{{count}} \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"},halfAMinute:"\u0E04\u0E23\u0E36\u0E48\u0E07\u0E19\u0E32\u0E17\u0E35",lessThanXMinutes:{one:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 1 \u0E19\u0E32\u0E17\u0E35",other:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 {{count}} \u0E19\u0E32\u0E17\u0E35"},xMinutes:{one:"1 \u0E19\u0E32\u0E17\u0E35",other:"{{count}} \u0E19\u0E32\u0E17\u0E35"},aboutXHours:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07"},xHours:{one:"1 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07",other:"{{count}} \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07"},xDays:{one:"1 \u0E27\u0E31\u0E19",other:"{{count}} \u0E27\u0E31\u0E19"},aboutXWeeks:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C"},xWeeks:{one:"1 \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",other:"{{count}} \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C"},aboutXMonths:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E40\u0E14\u0E37\u0E2D\u0E19",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E40\u0E14\u0E37\u0E2D\u0E19"},xMonths:{one:"1 \u0E40\u0E14\u0E37\u0E2D\u0E19",other:"{{count}} \u0E40\u0E14\u0E37\u0E2D\u0E19"},aboutXYears:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E1B\u0E35",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E1B\u0E35"},xYears:{one:"1 \u0E1B\u0E35",other:"{{count}} \u0E1B\u0E35"},overXYears:{one:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32 1 \u0E1B\u0E35",other:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32 {{count}} \u0E1B\u0E35"},almostXYears:{one:"\u0E40\u0E01\u0E37\u0E2D\u0E1A 1 \u0E1B\u0E35",other:"\u0E40\u0E01\u0E37\u0E2D\u0E1A {{count}} \u0E1B\u0E35"}},N=function E(B,C,U){var H,X=Z[B];if(typeof X==="string")H=X;else if(C===1)H=X.one;else H=X.other.replace("{{count}}",String(C));if(U!==null&&U!==void 0&&U.addSuffix)if(U.comparison&&U.comparison>0)if(B==="halfAMinute")return"\u0E43\u0E19"+H;else return"\u0E43\u0E19 "+H;else return H+"\u0E17\u0E35\u0E48\u0E1C\u0E48\u0E32\u0E19\u0E21\u0E32";return H};function x(E){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=B.width?String(B.width):E.defaultWidth,U=E.formats[C]||E.formats[E.defaultWidth];return U}}var z={full:"\u0E27\u0E31\u0E19EEEE\u0E17\u0E35\u0E48 do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"dd/MM/yyyy"},S={full:"H:mm:ss \u0E19. zzzz",long:"H:mm:ss \u0E19. z",medium:"H:mm:ss \u0E19.",short:"H:mm \u0E19."},M={full:"{{date}} '\u0E40\u0E27\u0E25\u0E32' {{time}}",long:"{{date}} '\u0E40\u0E27\u0E25\u0E32' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:x({formats:z,defaultWidth:"full"}),time:x({formats:S,defaultWidth:"medium"}),dateTime:x({formats:M,defaultWidth:"full"})},R={lastWeek:"eeee'\u0E17\u0E35\u0E48\u0E41\u0E25\u0E49\u0E27\u0E40\u0E27\u0E25\u0E32' p",yesterday:"'\u0E40\u0E21\u0E37\u0E48\u0E2D\u0E27\u0E32\u0E19\u0E19\u0E35\u0E49\u0E40\u0E27\u0E25\u0E32' p",today:"'\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49\u0E40\u0E27\u0E25\u0E32' p",tomorrow:"'\u0E1E\u0E23\u0E38\u0E48\u0E07\u0E19\u0E35\u0E49\u0E40\u0E27\u0E25\u0E32' p",nextWeek:"eeee '\u0E40\u0E27\u0E25\u0E32' p",other:"P"},V=function E(B,C,U,H){return R[B]};function O(E){return function(B,C){var U=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",H;if(U==="formatting"&&E.formattingValues){var X=E.defaultFormattingWidth||E.defaultWidth,I=C!==null&&C!==void 0&&C.width?String(C.width):X;H=E.formattingValues[I]||E.formattingValues[X]}else{var T=E.defaultWidth,D=C!==null&&C!==void 0&&C.width?String(C.width):E.defaultWidth;H=E.values[D]||E.values[T]}var A=E.argumentCallback?E.argumentCallback(B):B;return H[A]}}var f={narrow:["B","\u0E04\u0E28"],abbreviated:["BC","\u0E04.\u0E28."],wide:["\u0E1B\u0E35\u0E01\u0E48\u0E2D\u0E19\u0E04\u0E23\u0E34\u0E2A\u0E15\u0E01\u0E32\u0E25","\u0E04\u0E23\u0E34\u0E2A\u0E15\u0E4C\u0E28\u0E31\u0E01\u0E23\u0E32\u0E0A"]},j={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E41\u0E23\u0E01","\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E17\u0E35\u0E48\u0E2A\u0E2D\u0E07","\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E17\u0E35\u0E48\u0E2A\u0E32\u0E21","\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E17\u0E35\u0E48\u0E2A\u0E35\u0E48"]},_={narrow:["\u0E2D\u0E32.","\u0E08.","\u0E2D.","\u0E1E.","\u0E1E\u0E24.","\u0E28.","\u0E2A."],short:["\u0E2D\u0E32.","\u0E08.","\u0E2D.","\u0E1E.","\u0E1E\u0E24.","\u0E28.","\u0E2A."],abbreviated:["\u0E2D\u0E32.","\u0E08.","\u0E2D.","\u0E1E.","\u0E1E\u0E24.","\u0E28.","\u0E2A."],wide:["\u0E2D\u0E32\u0E17\u0E34\u0E15\u0E22\u0E4C","\u0E08\u0E31\u0E19\u0E17\u0E23\u0E4C","\u0E2D\u0E31\u0E07\u0E04\u0E32\u0E23","\u0E1E\u0E38\u0E18","\u0E1E\u0E24\u0E2B\u0E31\u0E2A\u0E1A\u0E14\u0E35","\u0E28\u0E38\u0E01\u0E23\u0E4C","\u0E40\u0E2A\u0E32\u0E23\u0E4C"]},w={narrow:["\u0E21.\u0E04.","\u0E01.\u0E1E.","\u0E21\u0E35.\u0E04.","\u0E40\u0E21.\u0E22.","\u0E1E.\u0E04.","\u0E21\u0E34.\u0E22.","\u0E01.\u0E04.","\u0E2A.\u0E04.","\u0E01.\u0E22.","\u0E15.\u0E04.","\u0E1E.\u0E22.","\u0E18.\u0E04."],abbreviated:["\u0E21.\u0E04.","\u0E01.\u0E1E.","\u0E21\u0E35.\u0E04.","\u0E40\u0E21.\u0E22.","\u0E1E.\u0E04.","\u0E21\u0E34.\u0E22.","\u0E01.\u0E04.","\u0E2A.\u0E04.","\u0E01.\u0E22.","\u0E15.\u0E04.","\u0E1E.\u0E22.","\u0E18.\u0E04."],wide:["\u0E21\u0E01\u0E23\u0E32\u0E04\u0E21","\u0E01\u0E38\u0E21\u0E20\u0E32\u0E1E\u0E31\u0E19\u0E18\u0E4C","\u0E21\u0E35\u0E19\u0E32\u0E04\u0E21","\u0E40\u0E21\u0E29\u0E32\u0E22\u0E19","\u0E1E\u0E24\u0E29\u0E20\u0E32\u0E04\u0E21","\u0E21\u0E34\u0E16\u0E38\u0E19\u0E32\u0E22\u0E19","\u0E01\u0E23\u0E01\u0E0E\u0E32\u0E04\u0E21","\u0E2A\u0E34\u0E07\u0E2B\u0E32\u0E04\u0E21","\u0E01\u0E31\u0E19\u0E22\u0E32\u0E22\u0E19","\u0E15\u0E38\u0E25\u0E32\u0E04\u0E21","\u0E1E\u0E24\u0E28\u0E08\u0E34\u0E01\u0E32\u0E22\u0E19","\u0E18\u0E31\u0E19\u0E27\u0E32\u0E04\u0E21"]},v={narrow:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E1A\u0E48\u0E32\u0E22",evening:"\u0E40\u0E22\u0E47\u0E19",night:"\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},abbreviated:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E1A\u0E48\u0E32\u0E22",evening:"\u0E40\u0E22\u0E47\u0E19",night:"\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},wide:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E1A\u0E48\u0E32\u0E22",evening:"\u0E40\u0E22\u0E47\u0E19",night:"\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"}},F={narrow:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E15\u0E2D\u0E19\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",evening:"\u0E15\u0E2D\u0E19\u0E40\u0E22\u0E47\u0E19",night:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},abbreviated:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E15\u0E2D\u0E19\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",evening:"\u0E15\u0E2D\u0E19\u0E40\u0E22\u0E47\u0E19",night:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},wide:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E15\u0E2D\u0E19\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",evening:"\u0E15\u0E2D\u0E19\u0E40\u0E22\u0E47\u0E19",night:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"}},P=function E(B,C){return String(B)},k={ordinalNumber:P,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",argumentCallback:function E(B){return B-1}}),month:O({values:w,defaultWidth:"wide"}),day:O({values:_,defaultWidth:"wide"}),dayPeriod:O({values:v,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"})};function Q(E){return function(B){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=C.width,H=U&&E.matchPatterns[U]||E.matchPatterns[E.defaultMatchWidth],X=B.match(H);if(!X)return null;var I=X[0],T=U&&E.parsePatterns[U]||E.parsePatterns[E.defaultParseWidth],D=Array.isArray(T)?h(T,function(W){return W.test(I)}):b(T,function(W){return W.test(I)}),A;A=E.valueCallback?E.valueCallback(D):D,A=C.valueCallback?C.valueCallback(A):A;var t=B.slice(I.length);return{value:A,rest:t}}}var b=function E(B,C){for(var U in B)if(Object.prototype.hasOwnProperty.call(B,U)&&C(B[U]))return U;return},h=function E(B,C){for(var U=0;U<B.length;U++)if(C(B[U]))return U;return};function m(E){return function(B){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=B.match(E.matchPattern);if(!U)return null;var H=U[0],X=B.match(E.parsePattern);if(!X)return null;var I=E.valueCallback?E.valueCallback(X[0]):X[0];I=C.valueCallback?C.valueCallback(I):I;var T=B.slice(H.length);return{value:I,rest:T}}}var c=/^\d+/i,u=/\d+/i,y={narrow:/^([bB]|[aA]|คศ)/i,abbreviated:/^([bB]\.?\s?[cC]\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?|ค\.?ศ\.?)/i,wide:/^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i},p={any:[/^[bB]/i,/^(^[aA]|ค\.?ศ\.?|คริสตกาล|คริสต์ศักราช|)/i]},g={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^ไตรมาส(ที่)? ?[1234]/i},d={any:[/(1|แรก|หนึ่ง)/i,/(2|สอง)/i,/(3|สาม)/i,/(4|สี่)/i]},l={narrow:/^(ม\.?ค\.?|ก\.?พ\.?|มี\.?ค\.?|เม\.?ย\.?|พ\.?ค\.?|มิ\.?ย\.?|ก\.?ค\.?|ส\.?ค\.?|ก\.?ย\.?|ต\.?ค\.?|พ\.?ย\.?|ธ\.?ค\.?)/i,abbreviated:/^(ม\.?ค\.?|ก\.?พ\.?|มี\.?ค\.?|เม\.?ย\.?|พ\.?ค\.?|มิ\.?ย\.?|ก\.?ค\.?|ส\.?ค\.?|ก\.?ย\.?|ต\.?ค\.?|พ\.?ย\.?|ธ\.?ค\.?')/i,wide:/^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i},i={wide:[/^มก/i,/^กุม/i,/^มี/i,/^เม/i,/^พฤษ/i,/^มิ/i,/^กรก/i,/^ส/i,/^กัน/i,/^ต/i,/^พฤศ/i,/^ธ/i],any:[/^ม\.?ค\.?/i,/^ก\.?พ\.?/i,/^มี\.?ค\.?/i,/^เม\.?ย\.?/i,/^พ\.?ค\.?/i,/^มิ\.?ย\.?/i,/^ก\.?ค\.?/i,/^ส\.?ค\.?/i,/^ก\.?ย\.?/i,/^ต\.?ค\.?/i,/^พ\.?ย\.?/i,/^ธ\.?ค\.?/i]},n={narrow:/^(อา\.?|จ\.?|อ\.?|พฤ\.?|พ\.?|ศ\.?|ส\.?)/i,short:/^(อา\.?|จ\.?|อ\.?|พฤ\.?|พ\.?|ศ\.?|ส\.?)/i,abbreviated:/^(อา\.?|จ\.?|อ\.?|พฤ\.?|พ\.?|ศ\.?|ส\.?)/i,wide:/^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i},s={wide:[/^อา/i,/^จั/i,/^อั/i,/^พุธ/i,/^พฤ/i,/^ศ/i,/^เส/i],any:[/^อา/i,/^จ/i,/^อ/i,/^พ(?!ฤ)/i,/^พฤ/i,/^ศ/i,/^ส/i]},o={any:/^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i},r={any:{am:/^ก่อนเที่ยง/i,pm:/^หลังเที่ยง/i,midnight:/^เที่ยงคืน/i,noon:/^เที่ยง/i,morning:/เช้า/i,afternoon:/บ่าย/i,evening:/เย็น/i,night:/กลางคืน/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:u,valueCallback:function E(B){return parseInt(B,10)}}),era:Q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function E(B){return B+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"th",formatDistance:N,formatLong:L,formatRelative:V,localize:k,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{th:a})})})();

//# debugId=386BB3035AEEB35864756e2164756e21
