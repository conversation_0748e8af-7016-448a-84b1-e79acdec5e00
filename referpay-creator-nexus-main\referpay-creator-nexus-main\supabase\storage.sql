-- Create storage buckets for file uploads

-- Create bucket for product images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('product-images', 'product-images', true);

-- Create bucket for product files
INSERT INTO storage.buckets (id, name, public) 
VALUES ('product-files', 'product-files', false);

-- Create bucket for user avatars
INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true);

-- Set up RLS policies for product-images bucket
CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'product-images');

CREATE POLICY "Authenticated users can upload images" ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'product-images' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update own images" ON storage.objects FOR UPDATE 
USING (bucket_id = 'product-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own images" ON storage.objects FOR DELETE 
USING (bucket_id = 'product-images' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Set up RLS policies for product-files bucket
CREATE POLICY "Authenticated users can upload files" ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'product-files' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update own files" ON storage.objects FOR UPDATE 
USING (bucket_id = 'product-files' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own files" ON storage.objects FOR DELETE 
USING (bucket_id = 'product-files' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Only allow file access to buyers who have purchased the product
CREATE POLICY "Buyers can access purchased files" ON storage.objects FOR SELECT 
USING (
  bucket_id = 'product-files' AND 
  EXISTS (
    SELECT 1 FROM purchases p
    JOIN products pr ON p.product_id = pr.id
    WHERE p.buyer_id = auth.uid()
    AND p.status = 'completed'
    AND pr.file_url LIKE '%' || name || '%'
  )
);

-- Creators can access their own files
CREATE POLICY "Creators can access own files" ON storage.objects FOR SELECT 
USING (
  bucket_id = 'product-files' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Set up RLS policies for avatars bucket
CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Authenticated users can upload avatars" ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update own avatars" ON storage.objects FOR UPDATE 
USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own avatars" ON storage.objects FOR DELETE 
USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
