-- Database Functions for Referpay

-- Function to generate unique referral codes
CREATE OR REPLACE FUNCTION public.generate_referral_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  exists_check BOOLEAN;
BEGIN
  LOOP
    -- Generate a random 8-character code
    code := 'REF_' || upper(substring(md5(random()::text) from 1 for 8));
    
    -- Check if code already exists
    SELECT EXISTS(SELECT 1 FROM public.referrals WHERE referral_code = code) INTO exists_check;
    
    -- Exit loop if code is unique
    EXIT WHEN NOT exists_check;
  END LOOP;
  
  RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Function to create a referral link
CREATE OR REPLACE FUNCTION public.create_referral(
  p_product_id UUID,
  p_referrer_id UUID DEFAULT auth.uid()
)
RETURNS TABLE(referral_code TEXT, referral_link TEXT) AS $$
DECLARE
  v_code TEXT;
  v_link TEXT;
  v_base_url TEXT := 'https://referpay.app'; -- Update with actual domain
BEGIN
  -- Check if referral already exists
  SELECT r.referral_code INTO v_code
  FROM public.referrals r
  WHERE r.product_id = p_product_id AND r.referrer_id = p_referrer_id;
  
  -- Create new referral if doesn't exist
  IF v_code IS NULL THEN
    v_code := public.generate_referral_code();
    
    INSERT INTO public.referrals (referrer_id, product_id, referral_code)
    VALUES (p_referrer_id, p_product_id, v_code);
  END IF;
  
  v_link := v_base_url || '/product/' || p_product_id || '?ref=' || v_code;
  
  RETURN QUERY SELECT v_code, v_link;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process referral commission
CREATE OR REPLACE FUNCTION public.process_referral_commission(
  p_purchase_id UUID
)
RETURNS VOID AS $$
DECLARE
  v_purchase RECORD;
  v_commission_rate DECIMAL := 0.33; -- 33% commission
  v_commission_amount DECIMAL;
BEGIN
  -- Get purchase details
  SELECT * INTO v_purchase
  FROM public.purchases
  WHERE id = p_purchase_id AND status = 'completed';
  
  IF v_purchase.referrer_id IS NOT NULL THEN
    -- Calculate commission (33% of product price)
    v_commission_amount := v_purchase.amount * v_commission_rate;
    
    -- Update referral record
    UPDATE public.referrals
    SET 
      commission_earned = commission_earned + v_commission_amount,
      clicks_count = clicks_count + 1,
      conversions_count = conversions_count + 1,
      updated_at = NOW()
    WHERE referrer_id = v_purchase.referrer_id 
    AND product_id = v_purchase.product_id;
    
    -- Update purchase with commission amount
    UPDATE public.purchases
    SET commission_amount = v_commission_amount
    WHERE id = p_purchase_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get product analytics
CREATE OR REPLACE FUNCTION public.get_product_analytics(
  p_product_id UUID,
  p_creator_id UUID DEFAULT auth.uid()
)
RETURNS TABLE(
  total_sales BIGINT,
  total_revenue DECIMAL,
  total_views BIGINT,
  conversion_rate DECIMAL,
  avg_rating DECIMAL,
  total_reviews BIGINT
) AS $$
BEGIN
  -- Verify ownership
  IF NOT EXISTS (
    SELECT 1 FROM public.products 
    WHERE id = p_product_id AND creator_id = p_creator_id
  ) THEN
    RAISE EXCEPTION 'Access denied: You do not own this product';
  END IF;
  
  RETURN QUERY
  SELECT 
    COALESCE(COUNT(pu.id), 0)::BIGINT as total_sales,
    COALESCE(SUM(pu.amount), 0) as total_revenue,
    COALESCE(pr.view_count, 0)::BIGINT as total_views,
    CASE 
      WHEN COALESCE(pr.view_count, 0) > 0 
      THEN (COALESCE(COUNT(pu.id), 0)::DECIMAL / pr.view_count) * 100
      ELSE 0
    END as conversion_rate,
    COALESCE(pr.rating_average, 0) as avg_rating,
    COALESCE(pr.rating_count, 0)::BIGINT as total_reviews
  FROM public.products pr
  LEFT JOIN public.purchases pu ON pr.id = pu.product_id AND pu.status = 'completed'
  WHERE pr.id = p_product_id
  GROUP BY pr.view_count, pr.rating_average, pr.rating_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user dashboard stats
CREATE OR REPLACE FUNCTION public.get_dashboard_stats(
  p_user_id UUID DEFAULT auth.uid()
)
RETURNS TABLE(
  total_products BIGINT,
  total_sales BIGINT,
  total_revenue DECIMAL,
  total_referrals BIGINT,
  referral_earnings DECIMAL,
  reputation_score INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM public.products WHERE creator_id = p_user_id)::BIGINT,
    (SELECT COUNT(*) FROM public.purchases pu 
     JOIN public.products pr ON pu.product_id = pr.id 
     WHERE pr.creator_id = p_user_id AND pu.status = 'completed')::BIGINT,
    (SELECT COALESCE(SUM(pu.amount - COALESCE(pu.commission_amount, 0)), 0) 
     FROM public.purchases pu 
     JOIN public.products pr ON pu.product_id = pr.id 
     WHERE pr.creator_id = p_user_id AND pu.status = 'completed'),
    (SELECT COUNT(*) FROM public.referrals WHERE referrer_id = p_user_id)::BIGINT,
    (SELECT COALESCE(SUM(commission_earned), 0) FROM public.referrals WHERE referrer_id = p_user_id),
    (SELECT COALESCE(reputation_score, 0) FROM public.profiles WHERE id = p_user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search products with filters
CREATE OR REPLACE FUNCTION public.search_products(
  p_query TEXT DEFAULT '',
  p_category product_category DEFAULT NULL,
  p_min_price DECIMAL DEFAULT 0,
  p_max_price DECIMAL DEFAULT 1000,
  p_min_rating DECIMAL DEFAULT 0,
  p_tags TEXT[] DEFAULT ARRAY[]::TEXT[],
  p_sort_by TEXT DEFAULT 'created_at',
  p_sort_order TEXT DEFAULT 'DESC',
  p_limit INTEGER DEFAULT 20,
  p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  price DECIMAL,
  category product_category,
  cover_image_url TEXT,
  rating_average DECIMAL,
  rating_count INTEGER,
  download_count INTEGER,
  created_at TIMESTAMPTZ,
  creator_name TEXT,
  creator_username TEXT
) AS $$
DECLARE
  v_query TEXT;
BEGIN
  -- Build dynamic query
  v_query := '
    SELECT 
      p.id,
      p.title,
      p.description,
      p.price,
      p.category,
      p.cover_image_url,
      p.rating_average,
      p.rating_count,
      p.download_count,
      p.created_at,
      pr.full_name as creator_name,
      pr.username as creator_username
    FROM public.products p
    JOIN public.profiles pr ON p.creator_id = pr.id
    WHERE p.is_active = true
  ';
  
  -- Add search conditions
  IF p_query != '' THEN
    v_query := v_query || ' AND (p.title ILIKE ''%' || p_query || '%'' OR p.description ILIKE ''%' || p_query || '%'')';
  END IF;
  
  IF p_category IS NOT NULL THEN
    v_query := v_query || ' AND p.category = ''' || p_category || '''';
  END IF;
  
  v_query := v_query || ' AND p.price BETWEEN ' || p_min_price || ' AND ' || p_max_price;
  v_query := v_query || ' AND p.rating_average >= ' || p_min_rating;
  
  IF array_length(p_tags, 1) > 0 THEN
    v_query := v_query || ' AND p.tags && ARRAY[' || array_to_string(p_tags, ',') || ']';
  END IF;
  
  -- Add sorting
  v_query := v_query || ' ORDER BY p.' || p_sort_by || ' ' || p_sort_order;
  v_query := v_query || ' LIMIT ' || p_limit || ' OFFSET ' || p_offset;
  
  RETURN QUERY EXECUTE v_query;
END;
$$ LANGUAGE plpgsql;

-- Function to handle profile creation after user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, full_name, role)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'buyer')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for automatic profile creation
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to increment product view count
CREATE OR REPLACE FUNCTION public.increment_view_count(p_product_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE public.products 
  SET view_count = COALESCE(view_count, 0) + 1
  WHERE id = p_product_id;
END;
$$ LANGUAGE plpgsql;

-- Function to track referral clicks
CREATE OR REPLACE FUNCTION public.track_referral_click(p_referral_code TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE public.referrals
  SET clicks_count = clicks_count + 1
  WHERE referral_code = p_referral_code;
END;
$$ LANGUAGE plpgsql;
