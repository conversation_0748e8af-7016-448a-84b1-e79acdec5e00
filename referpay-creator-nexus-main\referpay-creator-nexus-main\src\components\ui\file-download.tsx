import { useState } from 'react';
import { Button } from './button';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Download, Loader2, FileText, Image, Code, Package } from 'lucide-react';

interface FileDownloadProps {
  fileUrl: string;
  fileName: string;
  productTitle: string;
  purchaseId?: string;
  className?: string;
}

export const FileDownload = ({ 
  fileUrl, 
  fileName, 
  productTitle, 
  purchaseId,
  className = "" 
}: FileDownloadProps) => {
  const [downloading, setDownloading] = useState(false);
  const { toast } = useToast();

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText className="h-4 w-4" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return <Image className="h-4 w-4" />;
      case 'js':
      case 'ts':
      case 'html':
      case 'css':
      case 'py':
      case 'java':
        return <Code className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const handleDownload = async () => {
    if (!fileUrl) {
      toast({
        title: "خطأ",
        description: "رابط الملف غير متوفر",
        variant: "destructive",
      });
      return;
    }

    setDownloading(true);

    try {
      // Track download if purchaseId is provided
      if (purchaseId) {
        await supabase
          .from('purchases')
          .update({ 
            completed_at: new Date().toISOString() 
          })
          .eq('id', purchaseId);
      }

      // Create download link
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName || 'download';
      link.target = '_blank';
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "تم بنجاح",
        description: `تم تحميل ${productTitle}`,
      });

    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء التحميل",
        variant: "destructive",
      });
    } finally {
      setDownloading(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={downloading || !fileUrl}
      className={className}
      variant="default"
    >
      {downloading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          جاري التحميل...
        </>
      ) : (
        <>
          {getFileIcon(fileName)}
          <Download className="mr-2 h-4 w-4" />
          تحميل
        </>
      )}
    </Button>
  );
};

// Component for displaying file info
interface FileInfoProps {
  fileName: string;
  fileSize?: number;
  fileType?: string;
}

export const FileInfo = ({ fileName, fileSize, fileType }: FileInfoProps) => {
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'غير محدد';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return <Image className="h-5 w-5 text-blue-500" />;
      case 'js':
      case 'ts':
      case 'html':
      case 'css':
      case 'py':
      case 'java':
        return <Code className="h-5 w-5 text-green-500" />;
      default:
        return <Package className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
      {getFileIcon(fileName)}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {fileName}
        </p>
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          {fileType && <span>{fileType}</span>}
          {fileSize && <span>• {formatFileSize(fileSize)}</span>}
        </div>
      </div>
    </div>
  );
};
