
import { useState, useEffect } from 'react';
import { Header } from '@/components/layout/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PaymentModal } from '@/components/ui/payment-modal';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Product } from '@/types/database';
import {
  Search,
  Filter,
  Star,
  Download,
  DollarSign,
  BookOpen,
  Monitor,
  Palette,
  Code,
  Zap,
  ShoppingCart,
  Package
} from 'lucide-react';

const Marketplace = () => {
  const { toast } = useToast();
  const [language, setLanguage] = useState<'en' | 'ar'>('ar');
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get referral code from URL
  const urlParams = new URLSearchParams(window.location.search);
  const referralCode = urlParams.get('ref');

  const content = {
    en: {
      title: "Digital Marketplace",
      subtitle: "Discover amazing digital products from creators worldwide",
      searchPlaceholder: "Search for products...",
      filterButton: "Filter",
      categories: "Categories",
      allCategories: "All Categories",
      noResults: "No products found",
      noResultsDesc: "Try adjusting your search or filters",
      price: "Price",
      downloads: "downloads",
      rating: "rating"
    },
    ar: {
      title: "السوق الرقمي",
      subtitle: "اكتشف منتجات رقمية رائعة من منشئين حول العالم",
      searchPlaceholder: "البحث عن المنتجات...",
      filterButton: "تصفية",
      categories: "الفئات",
      allCategories: "جميع الفئات",
      noResults: "لا توجد منتجات",
      noResultsDesc: "حاول تعديل البحث أو الفلاتر",
      price: "السعر",
      downloads: "تحميل",
      rating: "التقييم"
    }
  };

  const t = content[language];

  useEffect(() => {
    loadProducts();
  }, [selectedCategory]);

  const loadProducts = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('products')
        .select(`
          *,
          creator:profiles(username, full_name)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (selectedCategory !== 'all') {
        query = query.eq('category', selectedCategory);
      }

      const { data, error } = await query;

      if (error) throw error;
      setProducts(data || []);
    } catch (error) {
      console.error('Error loading products:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحميل المنتجات",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBuyProduct = (product: Product) => {
    setSelectedProduct(product);
    setPaymentModalOpen(true);
  };

  const categories = [
    { id: 'all', name: language === 'ar' ? 'الكل' : 'All', icon: Zap },
    { id: 'ebook', name: language === 'ar' ? 'كتب' : 'Books', icon: BookOpen },
    { id: 'software', name: language === 'ar' ? 'برمجيات' : 'Software', icon: Code },
    { id: 'design', name: language === 'ar' ? 'تصميم' : 'Design', icon: Palette },
    { id: 'course', name: language === 'ar' ? 'دورات' : 'Courses', icon: Monitor }
  ];

  const filteredProducts = products.filter(product =>
    product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sampleProducts = [
    {
      id: 1,
      title: language === 'ar' ? 'دليل الاستثمار الرقمي' : 'Digital Investment Guide',
      description: language === 'ar' ? 'دليل شامل للاستثمار في العملات الرقمية' : 'Complete guide to cryptocurrency investment',
      price: 15,
      rating: 4.8,
      downloads: 234,
      category: 'ebook',
      image: '/lovable-uploads/b10097f0-bd5a-41dd-9a99-f53815bff45c.png'
    },
    {
      id: 2,
      title: language === 'ar' ? 'قالب موقع تجاري' : 'Business Website Template',
      description: language === 'ar' ? 'قالب احترافي للمواقع التجارية' : 'Professional template for business websites',
      price: 25,
      rating: 4.6,
      downloads: 189,
      category: 'design',
      image: '/placeholder.svg'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{t.title}</h1>
          <p className="text-gray-600">{t.subtitle}</p>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={t.searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            {t.filterButton}
          </Button>
        </div>

        {/* Categories */}
        <div className="flex gap-2 mb-8 overflow-x-auto">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className="flex items-center gap-2 whitespace-nowrap"
                onClick={() => setSelectedCategory(category.id)}
              >
                <Icon className="h-4 w-4" />
                {category.name}
              </Button>
            );
          })}
        </div>

        {/* Products Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                <CardHeader>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-lg transition-shadow">
                <div className="aspect-video bg-gray-100 rounded-t-lg flex items-center justify-center">
                  {product.cover_image_url ? (
                    <img
                      src={product.cover_image_url}
                      alt={product.title}
                      className="w-full h-full object-cover rounded-t-lg"
                    />
                  ) : (
                    <Package className="h-16 w-16 text-gray-400" />
                  )}
                </div>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{product.title}</CardTitle>
                    <Badge variant="secondary">{product.category}</Badge>
                  </div>
                  <CardDescription className="line-clamp-2">
                    {product.description}
                  </CardDescription>
                  {product.creator && (
                    <p className="text-xs text-gray-500">
                      بواسطة: {product.creator.full_name || product.creator.username}
                    </p>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">
                        {product.rating_average > 0 ? product.rating_average.toFixed(1) : 'جديد'}
                      </span>
                      {product.rating_count > 0 && (
                        <span className="text-xs text-gray-500">
                          ({product.rating_count})
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-1 text-gray-500">
                      <Download className="h-4 w-4" />
                      <span className="text-sm">{product.download_count} {t.downloads}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-bold text-xl">${product.price}</span>
                    </div>
                    <Button onClick={() => handleBuyProduct(product)}>
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'شراء الآن' : 'Buy Now'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {filteredProducts.length === 0 && !loading && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium mb-2">{t.noResults}</h3>
            <p className="text-gray-600">{t.noResultsDesc}</p>
          </div>
        )}

        {/* Payment Modal */}
        {selectedProduct && (
          <PaymentModal
            isOpen={paymentModalOpen}
            onClose={() => {
              setPaymentModalOpen(false);
              setSelectedProduct(null);
            }}
            product={selectedProduct}
            referralCode={referralCode || undefined}
          />
        )}
      </div>
    </div>
  );
};

export default Marketplace;
