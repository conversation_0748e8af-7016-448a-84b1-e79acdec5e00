{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"VITE_SUPABASE_URL": "@vite_supabase_url", "VITE_SUPABASE_ANON_KEY": "@vite_supabase_anon_key", "VITE_NOWPAYMENTS_API_KEY": "@vite_nowpayments_api_key", "VITE_BINANCE_PAY_API_KEY": "@vite_binance_pay_api_key", "VITE_TON_PAY_API_KEY": "@vite_ton_pay_api_key"}}