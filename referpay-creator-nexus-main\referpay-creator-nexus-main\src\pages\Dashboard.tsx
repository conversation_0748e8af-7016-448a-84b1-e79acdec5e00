import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileDownload } from '@/components/ui/file-download';
import { StatsCard, StatsGrid } from '@/components/ui/stats-card';
import { Loading, TableSkeleton } from '@/components/ui/loading';
import { useToast } from '@/hooks/use-toast';
import {
  Package,
  TrendingUp,
  Users,
  DollarSign,
  Plus,
  BarChart3,
  Eye,
  Download,
  Copy,
  Share2
} from 'lucide-react';
import { Product, PurchaseWithRelations, ReferralWithRelations } from '@/types/database';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  const { profile, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [products, setProducts] = useState<Product[]>([]);
  const [purchases, setPurchases] = useState<PurchaseWithRelations[]>([]);
  const [referrals, setReferrals] = useState<ReferralWithRelations[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isAuthenticated && profile) {
      loadDashboardData();
    }
  }, [isAuthenticated, profile]);

  const loadDashboardData = async () => {
    if (!profile) return;

    setLoading(true);
    try {
      // Load user's products
      const { data: productsData } = await supabase
        .from('products')
        .select('*')
        .eq('creator_id', profile.id)
        .order('created_at', { ascending: false });

      // Load user's purchases with proper error handling
      const { data: purchasesData } = await supabase
        .from('purchases')
        .select(`
          *,
          product:products(title, creator_id),
          referrer:profiles!referrer_id(username)
        `)
        .eq('buyer_id', profile.id)
        .order('created_at', { ascending: false });

      // Load user's referrals
      const { data: referralsData } = await supabase
        .from('referrals')
        .select(`
          *,
          product:products(title, price)
        `)
        .eq('referrer_id', profile.id)
        .order('created_at', { ascending: false });

      setProducts(productsData || []);
      setPurchases((purchasesData || []) as PurchaseWithRelations[]);
      setReferrals((referralsData || []) as ReferralWithRelations[]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">يرجى تسجيل الدخول</h2>
            <p className="text-gray-600">تحتاج إلى تسجيل الدخول للوصول إلى لوحة التحكم</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalEarnings = products.reduce((sum, product) => sum + (product.download_count * product.price), 0);
  const totalReferralEarnings = referrals.reduce((sum, referral) => sum + referral.commission_earned, 0);

  const generateReferralLink = async (productId: string) => {
    if (!profile) return;

    try {
      // Check if referral already exists
      const { data: existingReferral } = await supabase
        .from('referrals')
        .select('referral_code')
        .eq('referrer_id', profile.id)
        .eq('product_id', productId)
        .single();

      let referralCode = existingReferral?.referral_code;

      if (!referralCode) {
        // Generate new referral code
        referralCode = `REF_${Math.random().toString(36).substr(2, 8).toUpperCase()}`;

        const { error } = await supabase
          .from('referrals')
          .insert({
            referrer_id: profile.id,
            product_id: productId,
            referral_code: referralCode,
          });

        if (error) throw error;
      }

      const referralLink = `${window.location.origin}/?ref=${referralCode}`;

      // Copy to clipboard
      await navigator.clipboard.writeText(referralLink);

      toast({
        title: "تم النسخ!",
        description: "تم نسخ رابط الإحالة إلى الحافظة",
      });

    } catch (error) {
      console.error('Error generating referral link:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إنشاء رابط الإحالة",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">مرحباً، {profile?.full_name}</h1>
          <p className="text-gray-600">إليك نظرة عامة على نشاطك</p>
        </div>
        <Button asChild>
          <Link to="/create">
            <Plus className="ml-2 h-4 w-4" />
            منتج جديد
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <StatsGrid className="mb-8">
        <StatsCard
          title="إجمالي المنتجات"
          value={products.length}
          description="المنتجات المنشورة"
          icon={Package}
          variant="default"
        />

        <StatsCard
          title="الأرباح من المبيعات"
          value={`$${totalEarnings.toFixed(2)}`}
          description="إجمالي المبيعات"
          icon={DollarSign}
          variant="success"
          trend={{
            value: 12.5,
            label: "من الشهر الماضي",
            isPositive: true
          }}
        />

        <StatsCard
          title="أرباح الإحالة"
          value={`$${totalReferralEarnings.toFixed(2)}`}
          description="عمولات الإحالة"
          icon={TrendingUp}
          variant="success"
          trend={{
            value: 8.2,
            label: "من الشهر الماضي",
            isPositive: true
          }}
        />

        <StatsCard
          title="نقاط السمعة"
          value={profile?.reputation_score || 0}
          description="تقييم الحساب"
          icon={Users}
          variant="default"
        />
      </StatsGrid>

      {/* Detailed Tabs */}
      <Tabs defaultValue="products" className="space-y-6">
        <TabsList>
          <TabsTrigger value="products">منتجاتي</TabsTrigger>
          <TabsTrigger value="purchases">مشترياتي</TabsTrigger>
          <TabsTrigger value="referrals">إحالاتي</TabsTrigger>
        </TabsList>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>منتجاتي</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <TableSkeleton rows={3} />
              ) : products.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">لا توجد منتجات بعد</h3>
                  <p className="text-gray-600 mb-4">ابدأ بإضافة منتجك الأول</p>
                  <Button asChild>
                    <Link to="/create">
                      <Plus className="ml-2 h-4 w-4" />
                      إضافة منتج
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {products.map((product) => (
                    <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h3 className="font-medium">{product.title}</h3>
                        <p className="text-sm text-gray-600">{product.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge variant="secondary">{product.category}</Badge>
                          <span className="text-sm text-gray-500">
                            <Eye className="inline h-3 w-3 ml-1" />
                            {product.download_count} تحميل
                          </span>
                          <span className="text-sm text-gray-500">
                            السعر: ${product.price}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => generateReferralLink(product.id)}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          تعديل
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="purchases">
          <Card>
            <CardHeader>
              <CardTitle>مشترياتي</CardTitle>
            </CardHeader>
            <CardContent>
              {purchases.length === 0 ? (
                <div className="text-center py-8">
                  <Download className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">لا توجد مشتريات بعد</h3>
                  <p className="text-gray-600">استكشف السوق لإيجاد منتجات مفيدة</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {purchases.map((purchase) => (
                    <div key={purchase.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{purchase.product?.title}</h3>
                        <p className="text-sm text-gray-600">
                          تاريخ الشراء: {new Date(purchase.created_at).toLocaleDateString('ar')}
                        </p>
                        <Badge 
                          variant={purchase.status === 'completed' ? 'default' : 'secondary'}
                        >
                          {purchase.status === 'completed' ? 'مكتمل' : 'معلق'}
                        </Badge>
                      </div>
                      <div className="text-left">
                        <p className="font-medium">${purchase.amount}</p>
                        {purchase.status === 'completed' && purchase.product && (
                          <div className="space-y-2">
                            {/* Find the actual product to get file_url */}
                            {(() => {
                              const product = products.find(p => p.id === purchase.product_id);
                              return product?.file_url ? (
                                <FileDownload
                                  fileUrl={product.file_url}
                                  fileName={`${product.title}.${product.file_url.split('.').pop()}`}
                                  productTitle={product.title}
                                  purchaseId={purchase.id}
                                  className="w-full"
                                />
                              ) : (
                                <Button variant="outline" size="sm" disabled>
                                  ملف غير متوفر
                                </Button>
                              );
                            })()}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="referrals">
          <Card>
            <CardHeader>
              <CardTitle>إحالاتي</CardTitle>
            </CardHeader>
            <CardContent>
              {referrals.length === 0 ? (
                <div className="text-center py-8">
                  <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">لا توجد إحالات بعد</h3>
                  <p className="text-gray-600">ابدأ بمشاركة المنتجات لكسب العمولات</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {referrals.map((referral) => (
                    <div key={referral.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{referral.product?.title}</h3>
                        <p className="text-sm text-gray-600">
                          كود الإحالة: {referral.referral_code}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className="text-sm text-gray-500">
                            {referral.clicks} نقرة
                          </span>
                          <span className="text-sm text-gray-500">
                            {referral.conversions} تحويل
                          </span>
                        </div>
                      </div>
                      <div className="text-left">
                        <p className="font-medium text-green-600">
                          +${referral.commission_earned}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
