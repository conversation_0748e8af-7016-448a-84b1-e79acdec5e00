# Referpay - منصة التجارة الرقمية اللامركزية

## نظرة عامة

Referpay هي منصة رقمية لامركزية تمكن المنشئين من بيع منتجاتهم الرقمية مع نظام إحالة متقدم. المنصة تدعم العملات المشفرة وتوفر عمولة عادلة للمسوقين والمنشئين.

## الميزات الرئيسية

### 🚀 للمنشئين
- إنشاء وبيع المنتجات الرقمية بسهولة
- رفع الملفات والصور
- تتبع المبيعات والإحصائيات
- نظام تقييم وسمعة

### 💰 نظام الإحالة
- عمولة 33% للمسوقين
- روابط إحالة فريدة
- تتبع النقرات والتحويلات
- دفع تلقائي للعمولات

### 🔐 الأمان والدفع
- دعم متعدد للعملات المشفرة
- Binance Pay, TON Pay, NOWPayments
- حماية البيانات مع Row Level Security
- مصادقة آمنة عبر Supabase

### 🌍 متعدد اللغات
- دعم كامل للعربية والإنجليزية
- واجهة مستخدم متجاوبة
- تجربة محلية للمستخدمين العرب

## التقنيات المستخدمة

### Frontend
- **React 18** مع TypeScript
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - التصميم المتجاوب
- **shadcn/ui** - مكونات واجهة المستخدم
- **React Query** - إدارة البيانات
- **React Router** - التنقل

### Backend
- **Supabase** - Backend-as-a-Service
- **PostgreSQL** - قاعدة البيانات
- **Row Level Security** - الأمان
- **Real-time subscriptions** - التحديثات المباشرة

### المدفوعات
- **NOWPayments** - العملات المشفرة
- **Binance Pay** - محفظة Binance
- **TON Pay** - عملة TON

## التثبيت والتشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn أو pnpm

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd referpay-creator-nexus-main
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
```

قم بتحديث ملف `.env` بالقيم الصحيحة:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_NOWPAYMENTS_API_KEY=your_nowpayments_key
VITE_BINANCE_PAY_API_KEY=your_binance_pay_key
VITE_TON_PAY_API_KEY=your_ton_pay_key
```

4. **تشغيل الخادم المحلي**
```bash
npm run dev
```

5. **فتح المتصفح**
انتقل إلى `http://localhost:8080`

## الأوامر المتاحة

```bash
# تشغيل الخادم المحلي
npm run dev

# بناء المشروع للإنتاج
npm run build

# معاينة البناء
npm run preview

# فحص الكود
npm run lint

# تشغيل الاختبارات
npm run test

# تشغيل الاختبارات مع المراقبة
npm run test:watch

# تقرير التغطية
npm run test:coverage
```

## هيكل المشروع

```
src/
├── components/          # مكونات واجهة المستخدم
│   ├── auth/           # مكونات المصادقة
│   ├── layout/         # مكونات التخطيط
│   └── ui/             # مكونات الواجهة الأساسية
├── hooks/              # React Hooks مخصصة
├── integrations/       # تكاملات خارجية (Supabase)
├── lib/                # مكتبات مساعدة
├── pages/              # صفحات التطبيق
├── types/              # تعريفات TypeScript
└── __tests__/          # ملفات الاختبار
```

## قاعدة البيانات

المشروع يستخدم Supabase مع PostgreSQL وتشمل الجداول:

- `profiles` - ملفات المستخدمين
- `products` - المنتجات الرقمية
- `purchases` - المشتريات
- `referrals` - الإحالات
- `reviews` - التقييمات
- `founder_contributors` - المساهمين المؤسسين

## الأمان

- **Row Level Security (RLS)** على جميع الجداول
- **مصادقة آمنة** عبر Supabase Auth
- **تشفير البيانات** في النقل والتخزين
- **التحقق من الصلاحيات** على مستوى قاعدة البيانات

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للدعم والاستفسارات:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

---

**Referpay** - تمكين كل منشئ من تحقيق إمكاناته الكاملة 🚀
