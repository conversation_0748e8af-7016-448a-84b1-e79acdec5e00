import { renderHook, act } from '@testing-library/react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

// Mock Supabase
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            select: jest.fn(() => ({
              single: jest.fn(),
            })),
          })),
        })),
      })),
    })),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('useAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock getSession to return no session initially
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });

    // Mock onAuthStateChange to return a subscription
    mockSupabase.auth.onAuthStateChange.mockReturnValue({
      data: {
        subscription: {
          unsubscribe: jest.fn(),
        },
      },
    });
  });

  it('should initialize with no user', async () => {
    const { result } = renderHook(() => useAuth());

    expect(result.current.user).toBeNull();
    expect(result.current.profile).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should handle sign in', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' };
    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: mockUser, session: null },
      error: null,
    });

    const { result } = renderHook(() => useAuth());

    await act(async () => {
      const response = await result.current.signIn('<EMAIL>', 'password');
      expect(response.error).toBeNull();
    });

    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password',
    });
  });

  it('should handle sign up', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' };
    mockSupabase.auth.signUp.mockResolvedValue({
      data: { user: mockUser, session: null },
      error: null,
    });

    const { result } = renderHook(() => useAuth());

    await act(async () => {
      const response = await result.current.signUp('<EMAIL>', 'password', {
        full_name: 'Test User',
        username: 'testuser',
      });
      expect(response.error).toBeNull();
    });

    expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password',
      options: {
        data: {
          full_name: 'Test User',
          username: 'testuser',
        },
      },
    });
  });

  it('should handle sign out', async () => {
    mockSupabase.auth.signOut.mockResolvedValue({
      error: null,
    });

    const { result } = renderHook(() => useAuth());

    await act(async () => {
      const response = await result.current.signOut();
      expect(response.error).toBeNull();
    });

    expect(mockSupabase.auth.signOut).toHaveBeenCalled();
  });
});
