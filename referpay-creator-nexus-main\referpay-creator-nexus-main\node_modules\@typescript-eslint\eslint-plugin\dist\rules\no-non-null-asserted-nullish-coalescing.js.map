{"version": 3, "file": "no-non-null-asserted-nullish-coalescing.js", "sourceRoot": "", "sources": ["../../src/rules/no-non-null-asserted-nullish-coalescing.ts"], "names": [], "mappings": ";;AAGA,oEAAkE;AAClE,oDAA8D;AAE9D,kCAAoE;AAEpE,SAAS,uBAAuB,CAC9B,QAAiC,EACjC,IAAmB;IAEnB,OAAO,CACL,QAAQ,CAAC,UAAU,CAAC,IAAI,CACtB,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAChE;QACD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAChB,GAAG,CAAC,EAAE,CACJ,0BAA0B,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CACvE,CACF,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAC,UAAsB;IACxD,IAAI,UAAU,CAAC,IAAI,KAAK,8BAAc,CAAC,QAAQ,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,IAAI,CAAC;IAC3C,OAAO,kBAAkB,CAAC,QAAQ,IAAI,kBAAkB,CAAC,IAAI,IAAI,IAAI,CAAC;AACxE,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,yCAAyC;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,mFAAmF;YACrF,WAAW,EAAE,QAAQ;SACtB;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,kCAAkC,EAChC,sHAAsH;YACxH,sBAAsB,EAAE,gCAAgC;SACzD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,+DAA+D,CAC7D,IAAkC;gBAElC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;oBAChE,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAChD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;oBACnC,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC/D,IAAI,QAAQ,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;wBACzD,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,oCAAoC;oBAC/C;;;;;;;;;;;;sBAYE;oBACF,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,wBAAwB;4BACnC,GAAG,CAAC,KAAK;gCACP,MAAM,eAAe,GAAG,IAAA,iBAAU,EAChC,OAAO,CAAC,UAAU,CAAC,YAAY,CAC7B,IAAI,EACJ,gBAAQ,CAAC,4BAA4B,CACtC,EACD,wBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAC1D,CAAC;gCACF,OAAO,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;4BACvC,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}