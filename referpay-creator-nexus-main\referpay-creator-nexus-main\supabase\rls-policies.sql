-- Enhanced Row Level Security Policies

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.founder_contributors ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Products policies
CREATE POLICY "Active products are viewable by everyone" ON public.products
  FOR SELECT USING (is_active = true);

CREATE POLICY "Creators can view their own products" ON public.products
  FOR SELECT USING (auth.uid() = creator_id);

CREATE POLICY "Authenticated users can insert products" ON public.products
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = creator_id);

CREATE POLICY "Creators can update their own products" ON public.products
  FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Creators can delete their own products" ON public.products
  FOR DELETE USING (auth.uid() = creator_id);

-- Purchases policies
CREATE POLICY "Users can view their own purchases" ON public.purchases
  FOR SELECT USING (auth.uid() = buyer_id);

CREATE POLICY "Creators can view purchases of their products" ON public.purchases
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.products 
      WHERE id = product_id AND creator_id = auth.uid()
    )
  );

CREATE POLICY "Authenticated users can insert purchases" ON public.purchases
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = buyer_id);

CREATE POLICY "Users can update their own purchases" ON public.purchases
  FOR UPDATE USING (auth.uid() = buyer_id);

-- Referrals policies
CREATE POLICY "Users can view their own referrals" ON public.referrals
  FOR SELECT USING (auth.uid() = referrer_id);

CREATE POLICY "Product creators can view referrals for their products" ON public.referrals
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.products 
      WHERE id = product_id AND creator_id = auth.uid()
    )
  );

CREATE POLICY "Authenticated users can insert referrals" ON public.referrals
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = referrer_id);

CREATE POLICY "Users can update their own referrals" ON public.referrals
  FOR UPDATE USING (auth.uid() = referrer_id);

-- Reviews policies
CREATE POLICY "Reviews are viewable by everyone" ON public.reviews
  FOR SELECT USING (true);

CREATE POLICY "Verified buyers can insert reviews" ON public.reviews
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND 
    auth.uid() = buyer_id AND
    EXISTS (
      SELECT 1 FROM public.purchases 
      WHERE buyer_id = auth.uid() 
      AND product_id = public.reviews.product_id 
      AND status = 'completed'
    )
  );

CREATE POLICY "Users can update their own reviews" ON public.reviews
  FOR UPDATE USING (auth.uid() = buyer_id);

CREATE POLICY "Users can delete their own reviews" ON public.reviews
  FOR DELETE USING (auth.uid() = buyer_id);

-- Founder contributors policies (read-only for most users)
CREATE POLICY "Founder contributors are viewable by everyone" ON public.founder_contributors
  FOR SELECT USING (true);

-- Functions for common operations

-- Function to check if user has purchased a product
CREATE OR REPLACE FUNCTION public.has_purchased_product(product_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.purchases 
    WHERE buyer_id = auth.uid() 
    AND product_id = product_uuid 
    AND status = 'completed'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's total earnings
CREATE OR REPLACE FUNCTION public.get_user_earnings()
RETURNS TABLE(
  sales_earnings DECIMAL,
  referral_earnings DECIMAL,
  total_earnings DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(sales.total, 0) as sales_earnings,
    COALESCE(referrals.total, 0) as referral_earnings,
    COALESCE(sales.total, 0) + COALESCE(referrals.total, 0) as total_earnings
  FROM (
    SELECT SUM(p.amount - p.commission_amount) as total
    FROM public.purchases p
    JOIN public.products pr ON p.product_id = pr.id
    WHERE pr.creator_id = auth.uid() AND p.status = 'completed'
  ) sales
  CROSS JOIN (
    SELECT SUM(r.commission_earned) as total
    FROM public.referrals r
    WHERE r.referrer_id = auth.uid()
  ) referrals;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update product statistics
CREATE OR REPLACE FUNCTION public.update_product_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update download count and rating when a purchase is completed
  IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
    UPDATE public.products 
    SET download_count = download_count + 1
    WHERE id = NEW.product_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update product stats
CREATE TRIGGER update_product_stats_trigger
  AFTER UPDATE ON public.purchases
  FOR EACH ROW
  EXECUTE FUNCTION public.update_product_stats();

-- Function to update review statistics
CREATE OR REPLACE FUNCTION public.update_review_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Recalculate product rating when review is added/updated/deleted
  UPDATE public.products 
  SET 
    rating_average = (
      SELECT COALESCE(AVG(rating), 0) 
      FROM public.reviews 
      WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
    ),
    rating_count = (
      SELECT COUNT(*) 
      FROM public.reviews 
      WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
    )
  WHERE id = COALESCE(NEW.product_id, OLD.product_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers for review statistics
CREATE TRIGGER update_review_stats_insert_trigger
  AFTER INSERT ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION public.update_review_stats();

CREATE TRIGGER update_review_stats_update_trigger
  AFTER UPDATE ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION public.update_review_stats();

CREATE TRIGGER update_review_stats_delete_trigger
  AFTER DELETE ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION public.update_review_stats();
