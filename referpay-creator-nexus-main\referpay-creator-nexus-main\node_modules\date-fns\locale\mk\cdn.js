function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}(function (_window$dateFns) {var __defProp = Object.defineProperty;
  var __export = function __export(target, all) {
    for (var name in all)
    __defProp(target, name, {
      get: all[name],
      enumerable: true,
      configurable: true,
      set: function set(newValue) {return all[name] = function () {return newValue;};}
    });
  };

  // lib/locale/mk/_lib/formatDistance.mjs
  var formatDistanceLocale = {
    lessThanXSeconds: {
      one: "\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",
      other: "\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"
    },
    xSeconds: {
      one: "1 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",
      other: "{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"
    },
    halfAMinute: "\u043F\u043E\u043B\u043E\u0432\u0438\u043D\u0430 \u043C\u0438\u043D\u0443\u0442\u0430",
    lessThanXMinutes: {
      one: "\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 \u043C\u0438\u043D\u0443\u0442\u0430",
      other: "\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 {{count}} \u043C\u0438\u043D\u0443\u0442\u0438"
    },
    xMinutes: {
      one: "1 \u043C\u0438\u043D\u0443\u0442\u0430",
      other: "{{count}} \u043C\u0438\u043D\u0443\u0442\u0438"
    },
    aboutXHours: {
      one: "\u043E\u043A\u043E\u043B\u0443 1 \u0447\u0430\u0441",
      other: "\u043E\u043A\u043E\u043B\u0443 {{count}} \u0447\u0430\u0441\u0430"
    },
    xHours: {
      one: "1 \u0447\u0430\u0441",
      other: "{{count}} \u0447\u0430\u0441\u0430"
    },
    xDays: {
      one: "1 \u0434\u0435\u043D",
      other: "{{count}} \u0434\u0435\u043D\u0430"
    },
    aboutXWeeks: {
      one: "\u043E\u043A\u043E\u043B\u0443 1 \u043D\u0435\u0434\u0435\u043B\u0430",
      other: "\u043E\u043A\u043E\u043B\u0443 {{count}} \u043C\u0435\u0441\u0435\u0446\u0438"
    },
    xWeeks: {
      one: "1 \u043D\u0435\u0434\u0435\u043B\u0430",
      other: "{{count}} \u043D\u0435\u0434\u0435\u043B\u0438"
    },
    aboutXMonths: {
      one: "\u043E\u043A\u043E\u043B\u0443 1 \u043C\u0435\u0441\u0435\u0446",
      other: "\u043E\u043A\u043E\u043B\u0443 {{count}} \u043D\u0435\u0434\u0435\u043B\u0438"
    },
    xMonths: {
      one: "1 \u043C\u0435\u0441\u0435\u0446",
      other: "{{count}} \u043C\u0435\u0441\u0435\u0446\u0438"
    },
    aboutXYears: {
      one: "\u043E\u043A\u043E\u043B\u0443 1 \u0433\u043E\u0434\u0438\u043D\u0430",
      other: "\u043E\u043A\u043E\u043B\u0443 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"
    },
    xYears: {
      one: "1 \u0433\u043E\u0434\u0438\u043D\u0430",
      other: "{{count}} \u0433\u043E\u0434\u0438\u043D\u0438"
    },
    overXYears: {
      one: "\u043F\u043E\u0432\u0435\u045C\u0435 \u043E\u0434 1 \u0433\u043E\u0434\u0438\u043D\u0430",
      other: "\u043F\u043E\u0432\u0435\u045C\u0435 \u043E\u0434 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"
    },
    almostXYears: {
      one: "\u0431\u0435\u0437\u043C\u0430\u043B\u043A\u0443 1 \u0433\u043E\u0434\u0438\u043D\u0430",
      other: "\u0431\u0435\u0437\u043C\u0430\u043B\u043A\u0443 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"
    }
  };
  var formatDistance = function formatDistance(token, count, options) {
    var result;
    var tokenValue = formatDistanceLocale[token];
    if (typeof tokenValue === "string") {
      result = tokenValue;
    } else if (count === 1) {
      result = tokenValue.one;
    } else {
      result = tokenValue.other.replace("{{count}}", String(count));
    }
    if (options !== null && options !== void 0 && options.addSuffix) {
      if (options.comparison && options.comparison > 0) {
        return "\u0437\u0430 " + result;
      } else {
        return "\u043F\u0440\u0435\u0434 " + result;
      }
    }
    return result;
  };

  // lib/locale/_lib/buildFormatLongFn.mjs
  function buildFormatLongFn(args) {
    return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var width = options.width ? String(options.width) : args.defaultWidth;
      var format = args.formats[width] || args.formats[args.defaultWidth];
      return format;
    };
  }

  // lib/locale/mk/_lib/formatLong.mjs
  var dateFormats = {
    full: "EEEE, dd MMMM yyyy",
    long: "dd MMMM yyyy",
    medium: "dd MMM yyyy",
    short: "dd/MM/yyyy"
  };
  var timeFormats = {
    full: "HH:mm:ss zzzz",
    long: "HH:mm:ss z",
    medium: "HH:mm:ss",
    short: "H:mm"
  };
  var dateTimeFormats = {
    any: "{{date}} {{time}}"
  };
  var formatLong = {
    date: buildFormatLongFn({
      formats: dateFormats,
      defaultWidth: "full"
    }),
    time: buildFormatLongFn({
      formats: timeFormats,
      defaultWidth: "full"
    }),
    dateTime: buildFormatLongFn({
      formats: dateTimeFormats,
      defaultWidth: "any"
    })
  };

  // lib/toDate.mjs
  function toDate(argument) {
    var argStr = Object.prototype.toString.call(argument);
    if (argument instanceof Date || _typeof(argument) === "object" && argStr === "[object Date]") {
      return new argument.constructor(+argument);
    } else if (typeof argument === "number" || argStr === "[object Number]" || typeof argument === "string" || argStr === "[object String]") {
      return new Date(argument);
    } else {
      return new Date(NaN);
    }
  }

  // lib/_lib/defaultOptions.mjs
  function getDefaultOptions() {
    return defaultOptions;
  }
  function setDefaultOptions(newOptions) {
    defaultOptions = newOptions;
  }
  var defaultOptions = {};

  // lib/startOfWeek.mjs
  function startOfWeek(date, options) {var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _defaultOptions3$loca;
    var defaultOptions3 = getDefaultOptions();
    var weekStartsOn = (_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 || (_options$locale = options.locale) === null || _options$locale === void 0 || (_options$locale = _options$locale.options) === null || _options$locale === void 0 ? void 0 : _options$locale.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions3.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions3$loca = defaultOptions3.locale) === null || _defaultOptions3$loca === void 0 || (_defaultOptions3$loca = _defaultOptions3$loca.options) === null || _defaultOptions3$loca === void 0 ? void 0 : _defaultOptions3$loca.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0;
    var _date = toDate(date);
    var day = _date.getDay();
    var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
    _date.setDate(_date.getDate() - diff);
    _date.setHours(0, 0, 0, 0);
    return _date;
  }

  // lib/isSameWeek.mjs
  function isSameWeek(dateLeft, dateRight, options) {
    var dateLeftStartOfWeek = startOfWeek(dateLeft, options);
    var dateRightStartOfWeek = startOfWeek(dateRight, options);
    return +dateLeftStartOfWeek === +dateRightStartOfWeek;
  }

  // lib/locale/mk/_lib/formatRelative.mjs
  var _lastWeek = function lastWeek(day) {
    var weekday = weekdays[day];
    switch (day) {
      case 0:
      case 3:
      case 6:
        return "'\u043C\u0438\u043D\u0430\u0442\u0430\u0442\u0430 " + weekday + " \u0432\u043E' p";
      case 1:
      case 2:
      case 4:
      case 5:
        return "'\u043C\u0438\u043D\u0430\u0442\u0438\u043E\u0442 " + weekday + " \u0432\u043E' p";
    }
  };
  var thisWeek = function thisWeek(day) {
    var weekday = weekdays[day];
    switch (day) {
      case 0:
      case 3:
      case 6:
        return "'\u043E\u0432\u0430 " + weekday + " \u0432o' p";
      case 1:
      case 2:
      case 4:
      case 5:
        return "'\u043E\u0432\u043E\u0458 " + weekday + " \u0432o' p";
    }
  };
  var _nextWeek = function nextWeek(day) {
    var weekday = weekdays[day];
    switch (day) {
      case 0:
      case 3:
      case 6:
        return "'\u0441\u043B\u0435\u0434\u043D\u0430\u0442\u0430 " + weekday + " \u0432o' p";
      case 1:
      case 2:
      case 4:
      case 5:
        return "'\u0441\u043B\u0435\u0434\u043D\u0438\u043E\u0442 " + weekday + " \u0432o' p";
    }
  };
  var weekdays = [
  "\u043D\u0435\u0434\u0435\u043B\u0430",
  "\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A",
  "\u0432\u0442\u043E\u0440\u043D\u0438\u043A",
  "\u0441\u0440\u0435\u0434\u0430",
  "\u0447\u0435\u0442\u0432\u0440\u0442\u043E\u043A",
  "\u043F\u0435\u0442\u043E\u043A",
  "\u0441\u0430\u0431\u043E\u0442\u0430"];

  var formatRelativeLocale = {
    lastWeek: function lastWeek(date, baseDate, options) {
      var day = date.getDay();
      if (isSameWeek(date, baseDate, options)) {
        return thisWeek(day);
      } else {
        return _lastWeek(day);
      }
    },
    yesterday: "'\u0432\u0447\u0435\u0440\u0430 \u0432\u043E' p",
    today: "'\u0434\u0435\u043D\u0435\u0441 \u0432\u043E' p",
    tomorrow: "'\u0443\u0442\u0440\u0435 \u0432\u043E' p",
    nextWeek: function nextWeek(date, baseDate, options) {
      var day = date.getDay();
      if (isSameWeek(date, baseDate, options)) {
        return thisWeek(day);
      } else {
        return _nextWeek(day);
      }
    },
    other: "P"
  };
  var formatRelative = function formatRelative(token, date, baseDate, options) {
    var format = formatRelativeLocale[token];
    if (typeof format === "function") {
      return format(date, baseDate, options);
    }
    return format;
  };

  // lib/locale/_lib/buildLocalizeFn.mjs
  function buildLocalizeFn(args) {
    return function (value, options) {
      var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
      var valuesArray;
      if (context === "formatting" && args.formattingValues) {
        var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
        var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
        valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
      } else {
        var _defaultWidth = args.defaultWidth;
        var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
        valuesArray = args.values[_width] || args.values[_defaultWidth];
      }
      var index = args.argumentCallback ? args.argumentCallback(value) : value;
      return valuesArray[index];
    };
  }

  // lib/locale/mk/_lib/localize.mjs
  var eraValues = {
    narrow: ["\u043F\u0440.\u043D.\u0435.", "\u043D.\u0435."],
    abbreviated: ["\u043F\u0440\u0435\u0434 \u043D. \u0435.", "\u043D. \u0435."],
    wide: ["\u043F\u0440\u0435\u0434 \u043D\u0430\u0448\u0430\u0442\u0430 \u0435\u0440\u0430", "\u043D\u0430\u0448\u0430\u0442\u0430 \u0435\u0440\u0430"]
  };
  var quarterValues = {
    narrow: ["1", "2", "3", "4"],
    abbreviated: ["1-\u0432\u0438 \u043A\u0432.", "2-\u0440\u0438 \u043A\u0432.", "3-\u0442\u0438 \u043A\u0432.", "4-\u0442\u0438 \u043A\u0432."],
    wide: ["1-\u0432\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B", "2-\u0440\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B", "3-\u0442\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B", "4-\u0442\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]
  };
  var monthValues = {
    abbreviated: [
    "\u0458\u0430\u043D",
    "\u0444\u0435\u0432",
    "\u043C\u0430\u0440",
    "\u0430\u043F\u0440",
    "\u043C\u0430\u0458",
    "\u0458\u0443\u043D",
    "\u0458\u0443\u043B",
    "\u0430\u0432\u0433",
    "\u0441\u0435\u043F\u0442",
    "\u043E\u043A\u0442",
    "\u043D\u043E\u0435\u043C",
    "\u0434\u0435\u043A"],

    wide: [
    "\u0458\u0430\u043D\u0443\u0430\u0440\u0438",
    "\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438",
    "\u043C\u0430\u0440\u0442",
    "\u0430\u043F\u0440\u0438\u043B",
    "\u043C\u0430\u0458",
    "\u0458\u0443\u043D\u0438",
    "\u0458\u0443\u043B\u0438",
    "\u0430\u0432\u0433\u0443\u0441\u0442",
    "\u0441\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438",
    "\u043E\u043A\u0442\u043E\u043C\u0432\u0440\u0438",
    "\u043D\u043E\u0435\u043C\u0432\u0440\u0438",
    "\u0434\u0435\u043A\u0435\u043C\u0432\u0440\u0438"]

  };
  var dayValues = {
    narrow: ["\u041D", "\u041F", "\u0412", "\u0421", "\u0427", "\u041F", "\u0421"],
    short: ["\u043D\u0435", "\u043F\u043E", "\u0432\u0442", "\u0441\u0440", "\u0447\u0435", "\u043F\u0435", "\u0441\u0430"],
    abbreviated: ["\u043D\u0435\u0434", "\u043F\u043E\u043D", "\u0432\u0442\u043E", "\u0441\u0440\u0435", "\u0447\u0435\u0442", "\u043F\u0435\u0442", "\u0441\u0430\u0431"],
    wide: [
    "\u043D\u0435\u0434\u0435\u043B\u0430",
    "\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A",
    "\u0432\u0442\u043E\u0440\u043D\u0438\u043A",
    "\u0441\u0440\u0435\u0434\u0430",
    "\u0447\u0435\u0442\u0432\u0440\u0442\u043E\u043A",
    "\u043F\u0435\u0442\u043E\u043A",
    "\u0441\u0430\u0431\u043E\u0442\u0430"]

  };
  var dayPeriodValues = {
    wide: {
      am: "\u043F\u0440\u0435\u0442\u043F\u043B\u0430\u0434\u043D\u0435",
      pm: "\u043F\u043E\u043F\u043B\u0430\u0434\u043D\u0435",
      midnight: "\u043F\u043E\u043B\u043D\u043E\u045C",
      noon: "\u043D\u0430\u043F\u043B\u0430\u0434\u043D\u0435",
      morning: "\u043D\u0430\u0443\u0442\u0440\u043E",
      afternoon: "\u043F\u043E\u043F\u043B\u0430\u0434\u043D\u0435",
      evening: "\u043D\u0430\u0432\u0435\u0447\u0435\u0440",
      night: "\u043D\u043E\u045C\u0435"
    }
  };
  var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
    var number = Number(dirtyNumber);
    var rem100 = number % 100;
    if (rem100 > 20 || rem100 < 10) {
      switch (rem100 % 10) {
        case 1:
          return number + "-\u0432\u0438";
        case 2:
          return number + "-\u0440\u0438";
        case 7:
        case 8:
          return number + "-\u043C\u0438";
      }
    }
    return number + "-\u0442\u0438";
  };
  var localize = {
    ordinalNumber: ordinalNumber,
    era: buildLocalizeFn({
      values: eraValues,
      defaultWidth: "wide"
    }),
    quarter: buildLocalizeFn({
      values: quarterValues,
      defaultWidth: "wide",
      argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
    }),
    month: buildLocalizeFn({
      values: monthValues,
      defaultWidth: "wide"
    }),
    day: buildLocalizeFn({
      values: dayValues,
      defaultWidth: "wide"
    }),
    dayPeriod: buildLocalizeFn({
      values: dayPeriodValues,
      defaultWidth: "wide"
    })
  };

  // lib/locale/_lib/buildMatchFn.mjs
  function buildMatchFn(args) {
    return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var width = options.width;
      var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
      var matchResult = string.match(matchPattern);
      if (!matchResult) {
        return null;
      }
      var matchedString = matchResult[0];
      var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
      var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
      var value;
      value = args.valueCallback ? args.valueCallback(key) : key;
      value = options.valueCallback ? options.valueCallback(value) : value;
      var rest = string.slice(matchedString.length);
      return { value: value, rest: rest };
    };
  }
  var findKey = function findKey(object, predicate) {
    for (var key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
        return key;
      }
    }
    return;
  };
  var findIndex = function findIndex(array, predicate) {
    for (var key = 0; key < array.length; key++) {
      if (predicate(array[key])) {
        return key;
      }
    }
    return;
  };

  // lib/locale/_lib/buildMatchPatternFn.mjs
  function buildMatchPatternFn(args) {
    return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var matchResult = string.match(args.matchPattern);
      if (!matchResult)
      return null;
      var matchedString = matchResult[0];
      var parseResult = string.match(args.parsePattern);
      if (!parseResult)
      return null;
      var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
      value = options.valueCallback ? options.valueCallback(value) : value;
      var rest = string.slice(matchedString.length);
      return { value: value, rest: rest };
    };
  }

  // lib/locale/mk/_lib/match.mjs
  var matchOrdinalNumberPattern = /^(\d+)(-?[врмт][и])?/i;
  var parseOrdinalNumberPattern = /\d+/i;
  var matchEraPatterns = {
    narrow: /^((пр)?н\.?\s?е\.?)/i,
    abbreviated: /^((пр)?н\.?\s?е\.?)/i,
    wide: /^(пред нашата ера|нашата ера)/i
  };
  var parseEraPatterns = {
    any: [/^п/i, /^н/i]
  };
  var matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /^[1234](-?[врт]?и?)? кв.?/i,
    wide: /^[1234](-?[врт]?и?)? квартал/i
  };
  var parseQuarterPatterns = {
    any: [/1/i, /2/i, /3/i, /4/i]
  };
  var matchDayPatterns = {
    narrow: /^[нпвсч]/i,
    short: /^(не|по|вт|ср|че|пе|са)/i,
    abbreviated: /^(нед|пон|вто|сре|чет|пет|саб)/i,
    wide: /^(недела|понеделник|вторник|среда|четврток|петок|сабота)/i
  };
  var parseDayPatterns = {
    narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],
    any: [/^н[ед]/i, /^п[он]/i, /^вт/i, /^ср/i, /^ч[ет]/i, /^п[ет]/i, /^с[аб]/i]
  };
  var matchMonthPatterns = {
    abbreviated: /^(јан|фев|мар|апр|мај|јун|јул|авг|сеп|окт|ноем|дек)/i,
    wide: /^(јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)/i
  };
  var parseMonthPatterns = {
    any: [
    /^ја/i,
    /^Ф/i,
    /^мар/i,
    /^ап/i,
    /^мај/i,
    /^јун/i,
    /^јул/i,
    /^ав/i,
    /^се/i,
    /^окт/i,
    /^но/i,
    /^де/i]

  };
  var matchDayPeriodPatterns = {
    any: /^(претп|попл|полноќ|утро|пладне|вечер|ноќ)/i
  };
  var parseDayPeriodPatterns = {
    any: {
      am: /претпладне/i,
      pm: /попладне/i,
      midnight: /полноќ/i,
      noon: /напладне/i,
      morning: /наутро/i,
      afternoon: /попладне/i,
      evening: /навечер/i,
      night: /ноќе/i
    }
  };
  var match = {
    ordinalNumber: buildMatchPatternFn({
      matchPattern: matchOrdinalNumberPattern,
      parsePattern: parseOrdinalNumberPattern,
      valueCallback: function valueCallback(value) {return parseInt(value, 10);}
    }),
    era: buildMatchFn({
      matchPatterns: matchEraPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseEraPatterns,
      defaultParseWidth: "any"
    }),
    quarter: buildMatchFn({
      matchPatterns: matchQuarterPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseQuarterPatterns,
      defaultParseWidth: "any",
      valueCallback: function valueCallback(index) {return index + 1;}
    }),
    month: buildMatchFn({
      matchPatterns: matchMonthPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseMonthPatterns,
      defaultParseWidth: "any"
    }),
    day: buildMatchFn({
      matchPatterns: matchDayPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseDayPatterns,
      defaultParseWidth: "any"
    }),
    dayPeriod: buildMatchFn({
      matchPatterns: matchDayPeriodPatterns,
      defaultMatchWidth: "any",
      parsePatterns: parseDayPeriodPatterns,
      defaultParseWidth: "any"
    })
  };

  // lib/locale/mk.mjs
  var mk = {
    code: "mk",
    formatDistance: formatDistance,
    formatLong: formatLong,
    formatRelative: formatRelative,
    localize: localize,
    match: match,
    options: {
      weekStartsOn: 1,
      firstWeekContainsDate: 4
    }
  };

  // lib/locale/mk/cdn.js
  window.dateFns = _objectSpread(_objectSpread({},
  window.dateFns), {}, {
    locale: _objectSpread(_objectSpread({}, (_window$dateFns =
    window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
      mk: mk }) });



  //# debugId=850F4ED48DF73F6064756e2164756e21
})();

//# sourceMappingURL=cdn.js.map