{"version": 3, "file": "index.umd.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isPlainObject.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/compact.ts", "../src/utils/isUndefined.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/isKey.ts", "../src/utils/stringToPath.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/utils/isEmptyObject.ts", "../src/logic/shouldRenderFormState.ts", "../src/utils/convertToArrayPayload.ts", "../src/logic/shouldSubscribeByName.ts", "../src/useSubscribe.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/logic/getValidationModes.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMessage.ts", "../src/utils/isRadioInput.ts", "../src/utils/isRegex.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getRadioValue.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/unset.ts", "../src/utils/update.ts", "../src/utils/createSubject.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/utils/live.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/logic/getRuleValue.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/hasValidation.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/skipValidation.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/createFormControl.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || data instanceof FileList)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TransformedValues extends FieldValues | undefined = undefined,\n>(): UseFormReturn<TFieldValues, TContext, TransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <TFieldValues extends FieldValues, TContext = any>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  Control,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & { name?: InternalFieldName },\n  _proxyFormState: K,\n  updateFormState: Control<T>['_updateFormState'],\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import React from 'react';\n\nimport { Subject } from './utils/createSubject';\n\ntype Props<T> = {\n  disabled?: boolean;\n  subject: Subject<T>;\n  next: (value: T) => void;\n};\n\nexport function useSubscribe<T>(props: Props<T>) {\n  const _props = React.useRef(props);\n  _props.current = props;\n\n  React.useEffect(() => {\n    const subscription =\n      !props.disabled &&\n      _props.current.subject &&\n      _props.current.subject.subscribe({\n        next: _props.current.next,\n      });\n\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState<TFieldValues extends FieldValues = FieldValues>(\n  props?: UseFormStateProps<TFieldValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _mounted = React.useRef(true);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) =>\n      _mounted.current &&\n      shouldSubscribeByName(\n        _name.current as InternalFieldName,\n        value.name,\n        exact,\n      ) &&\n      shouldRenderFormState(\n        value,\n        _localProxyFormState.current,\n        control._updateFormState,\n      ) &&\n      updateFormState({\n        ...control._formState,\n        ...value,\n      }),\n    subject: control._subjects.state,\n  });\n\n  React.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n\n  return getProxyFormState(\n    formState,\n    control,\n    _localProxyFormState.current,\n    false,\n  );\n}\n\nexport { useFormState };\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport cloneObject from './utils/cloneObject';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: (formState: { name?: InternalFieldName; values?: FieldValues }) => {\n      if (\n        shouldSubscribeByName(\n          _name.current as InternalFieldName,\n          formState.name,\n          exact,\n        )\n      ) {\n        updateValue(\n          cloneObject(\n            generateWatchOutput(\n              _name.current as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              defaultValue,\n            ),\n          ),\n        );\n      }\n    },\n  });\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: UseControllerProps<TFieldValues, TName>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    if (get(control._fields, name)) {\n      control._updateDisabledField({\n        disabled,\n        fields: control._fields,\n        name,\n        value: get(control._fields, name)._f.value,\n      });\n    }\n  }, [disabled, name, control]);\n\n  return {\n    field: {\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange: React.useCallback(\n        (event) =>\n          _registerProps.current.onChange({\n            target: {\n              value: getEventValue(event),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.CHANGE,\n          }),\n        [name],\n      ),\n      onBlur: React.useCallback(\n        () =>\n          _registerProps.current.onBlur({\n            target: {\n              value: get(control._formValues, name),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.BLUR,\n          }),\n        [name, control],\n      ),\n      ref: React.useCallback(\n        (elm) => {\n          const field = get(control._fields, name);\n\n          if (field && elm) {\n            field._f.ref = {\n              focus: () => elm.focus(),\n              select: () => elm.select(),\n              setCustomValidity: (message: string) =>\n                elm.setCustomValidity(message),\n              reportValidity: () => elm.reportValidity(),\n            };\n          }\n        },\n        [control._fields, name],\n      ),\n    },\n    formState,\n    fieldState: Object.defineProperties(\n      {},\n      {\n        invalid: {\n          enumerable: true,\n          get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n          enumerable: true,\n          get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n          enumerable: true,\n          get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n          enumerable: true,\n          get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n          enumerable: true,\n          get: () => get(formState.errors, name),\n        },\n      },\n    ) as ControllerFieldState,\n  };\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: ControllerProps<TFieldValues, TName>,\n) => props.render(useController<TFieldValues, TName>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key])) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  T extends FieldValues,\n  U extends FieldValues | undefined = undefined,\n>(props: FormProps<T, U>) {\n  const methods = useFormContext<T>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(action, {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n    disabled,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabled) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (_f.refs ? _f.refs.every((ref) => ref.disabled) : ref.disabled) {\n    return;\n  }\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  (!fieldReference || !fieldReference.validate) &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  PathValue,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): Omit<UseFormReturn<TFieldValues, TContext>, 'formState'> {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? {}\n    : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _updateValid = async (shouldUpdateValid?: boolean) => {\n    if (!props.disabled && (_proxyFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _executeSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !props.disabled &&\n      (_proxyFormState.isValidating || _proxyFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _updateFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !props.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        _proxyFormState.touchedFields &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _updateValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!props.disabled) {\n      const disabledField = !!(\n        get(_fields, name) &&\n        get(_fields, name)._f &&\n        get(_fields, name)._f.disabled\n      );\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine =\n          disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n\n        isPreviousDirty = !!(\n          !disabledField && get(_formState.dirtyFields, name)\n        );\n        isCurrentFieldPristine || disabledField\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          (_proxyFormState.dirtyFields &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            (_proxyFormState.touchedFields &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      _proxyFormState.isValid &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (props.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(props.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _executeSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _executeSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !props.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        props.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: { ..._formValues },\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: { ..._formValues },\n      });\n\n      if (\n        (_proxyFormState.isDirty || _proxyFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.values.next({\n      name: _state.mount ? name : undefined,\n      values: { ..._formValues },\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name as string;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const getCurrentFieldValue = () =>\n      target.type ? getFieldValue(field._f) : getEventValue(event);\n    const _updateIsFieldValueUpdated = (fieldValue: any): void => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(\n        name,\n        fieldValue,\n        isBlurEvent,\n        false,\n      );\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.values.next({\n          name,\n          type: event.type,\n          values: { ..._formValues },\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid) {\n          if (props.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _updateValid();\n            }\n          } else {\n            _updateValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _executeSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      (_proxyFormState.isValid && isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.values.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.values.next({\n      values: { ..._formValues },\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _updateValid();\n  };\n\n  const _updateDisabledField: Control<TFieldValues>['_updateDisabledField'] = ({\n    disabled,\n    name,\n    field,\n    fields,\n    value,\n  }) => {\n    if ((isBoolean(disabled) && _state.mount) || !!disabled) {\n      const inputValue = disabled\n        ? undefined\n        : isUndefined(value)\n          ? getFieldValue(field ? field._f : get(fields, name)._f)\n          : value;\n      set(_formValues, name, inputValue);\n      updateTouchAndDirty(name, inputValue, false, false, true);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(props.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _updateDisabledField({\n        field,\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : props.disabled,\n        name,\n        value: options.value,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || props.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        e.persist && e.persist();\n      }\n      let fieldValues = cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _executeSchema();\n        _formState.errors = errors;\n        fieldValues = values;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TFieldValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as PathValue<\n            TFieldValues,\n            FieldPath<TFieldValues>\n          >,\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        _fields = {};\n      }\n\n      _formValues = props.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? cloneObject(_defaultValues)\n          : {}\n        : cloneObject(values);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.values.next({\n        values: { ...values },\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!props.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && fieldRef.select();\n      }\n    }\n  };\n\n  const _updateFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _updateDisabledField,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      _setErrors,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n}\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n>(\n  props: UseFieldArrayProps<TFieldValues, TFieldArrayName, TKeyName>,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  props.rules &&\n    (control as Control<TFieldValues>).register(\n      name as FieldPath<TFieldValues>,\n      props.rules as RegisterOptions<TFieldValues>,\n    );\n\n  useSubscribe({\n    next: ({\n      values,\n      name: fieldArrayName,\n    }: {\n      values?: FieldValues;\n      name?: InternalFieldName;\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array,\n  });\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._updateFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted)\n    ) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.values.next({\n      name,\n      values: { ...control._formValues },\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._updateValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) &&\n        control.unregister(name as FieldPath<TFieldValues>);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport { createFormControl } from './logic/createFormControl';\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >();\n  const _values = React.useRef<typeof props.values>();\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props),\n      formState,\n    };\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useSubscribe({\n    subject: control._subjects.state,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) => {\n      if (\n        shouldRenderFormState(\n          value,\n          control._proxyFormState,\n          control._updateFormState,\n          true,\n        )\n      ) {\n        updateFormState({ ...control._formState });\n      }\n    },\n  });\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.values.next({\n        values: control._getWatch(),\n      });\n  }, [props.shouldUnregister, control]);\n\n  React.useEffect(() => {\n    if (_formControl.current) {\n      _formControl.current.watch = _formControl.current.watch.bind({});\n    }\n  }, [formState]);\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isPlainObject", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "Set", "Blob", "FileList", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "object", "path", "defaultValue", "result", "split", "reduce", "isBoolean", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "BLUR", "FOCUS_OUT", "CHANGE", "VALIDATION_MODE", "onBlur", "onChange", "onSubmit", "onTouched", "all", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "isEmptyObject", "keys", "shouldRenderFormState", "formStateData", "updateFormState", "find", "convertToArrayPayload", "shouldSubscribeByName", "signalName", "exact", "some", "currentName", "startsWith", "useSubscribe", "props", "_props", "useRef", "current", "useEffect", "subscription", "disabled", "subject", "subscribe", "next", "unsubscribe", "useFormState", "methods", "useState", "_formState", "_mounted", "_localProxyFormState", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_name", "_updateFormState", "_subjects", "state", "_updateValid", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "values", "updateValue", "_formValues", "_getWatch", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_registerProps", "register", "rules", "_shouldUnregisterField", "_options", "updateMounted", "field", "_fields", "_f", "mount", "_state", "action", "unregister", "_updateDisabledField", "fields", "useCallback", "ref", "elm", "focus", "select", "setCustomValidity", "message", "reportValidity", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "flatten", "obj", "output", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "options", "shouldFocus", "focusName", "focusIndex", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "isWatched", "isBlurEvent", "watchName", "slice", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "refs", "updateFieldArrayRootError", "fieldArrayErrors", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMessage", "isRadioInput", "isRegex", "RegExp", "defaultResult", "validResult", "getCheckboxValue", "option", "attributes", "defaultReturn", "getRadioValue", "previous", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "valueAsNumber", "inputValue", "inputRef", "isRadio", "isCheckBox", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "valueAsDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "from", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "unset", "paths", "childObject", "updatePath", "baseGet", "isEmptyArray", "updateAt", "field<PERSON><PERSON><PERSON>", "createSubject", "_observers", "observers", "observer", "push", "o", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys2", "val1", "includes", "val2", "isMultipleSelect", "live", "isConnected", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "getFieldValueAs", "setValueAs", "NaN", "getFieldValue", "files", "selectedOptions", "getResolverOptions", "criteriaMode", "getRuleValue", "rule", "source", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validateFunction", "hasValidation", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "pop", "skipValidation", "isSubmitted", "reValidateMode", "unsetEmptyArray", "defaultOptions", "shouldFocusError", "createFormControl", "delayError<PERSON><PERSON><PERSON>", "submitCount", "isSubmitting", "isSubmitSuccessful", "unMount", "timer", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldDisplayAllAssociatedErrors", "shouldUpdateValid", "resolver", "_executeSchema", "executeBuiltInValidation", "_updateIsValidating", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "<PERSON><PERSON><PERSON>", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "callback", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "shouldSkipValidation", "deps", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_updateFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "_setErrors", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "render", "mounted", "setMounted", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "body", "status", "String", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "useMemo", "_formControl", "_values"], "mappings": "wRAEA,IAAAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLDO,EAACC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IEHfK,EAACC,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACElB,EAASgB,IAAkBA,EAAcG,eAAe,gBACxD,ECRWC,EAAkB,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEe,SAAAC,EAAeC,GACrC,IAAIC,EACJ,MAAMxB,EAAUD,MAAMC,QAAQuB,GAE9B,GAAIA,aAAgB5B,KAClB6B,EAAO,IAAI7B,KAAK4B,QACX,GAAIA,aAAgBE,IACzBD,EAAO,IAAIC,IAAIF,OACV,IACHL,IAAUK,aAAgBG,MAAQH,aAAgBI,YACnD3B,IAAWF,EAASyB,GAcrB,OAAOA,EAVP,GAFAC,EAAOxB,EAAU,GAAK,GAEjBA,GAAYY,EAAcW,GAG7B,IAAK,MAAMK,KAAOL,EACZA,EAAKN,eAAeW,KACtBJ,EAAKI,GAAON,EAAYC,EAAKK,UAJjCJ,EAAOD,CAUV,CAED,OAAOC,CACT,CChCA,IAAAK,EAAwBnC,GACtBK,MAAMC,QAAQN,GAASA,EAAMoC,OAAOC,SAAW,GCDjDC,EAAgBC,QAA2CC,IAARD,ECKnDE,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAASvC,EAASsC,GACrB,OAAOE,EAGT,MAAMC,EAASV,EAAQQ,EAAKG,MAAM,cAAcC,QAC9C,CAACF,EAAQX,IACPhC,EAAkB2C,GAAUA,EAASA,EAAOX,IAC9CQ,GAGF,OAAOJ,EAAYO,IAAWA,IAAWH,EACrCJ,EAAYI,EAAOC,IACjBC,EACAF,EAAOC,GACTE,CAAM,ECxBZG,EAAgBhD,GAAsD,kBAAVA,ECA7CiD,EAACjD,GAAkB,QAAQkD,KAAKlD,GCE/CmD,EAAgBC,GACdjB,EAAQiB,EAAMC,QAAQ,YAAa,IAAIP,MAAM,UCG/CQ,EAAe,CACbZ,EACAC,EACA3C,KAEA,IAAIuD,GAAS,EACb,MAAMC,EAAWP,EAAMN,GAAQ,CAACA,GAAQQ,EAAaR,GAC/Cc,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW3D,EAEf,GAAIuD,IAAUG,EAAW,CACvB,MAAME,EAAWlB,EAAOR,GACxByB,EACEvD,EAASwD,IAAavD,MAAMC,QAAQsD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,GADA,EAET,CAED,GAAY,cAARrB,EACF,OAGFQ,EAAOR,GAAOyB,EACdjB,EAASA,EAAOR,EACjB,CACD,OAAOQ,CAAM,ECrCR,MAAMoB,EAAS,CACpBC,KAAM,OACNC,UAAW,WACXC,OAAQ,UAGGC,EAAkB,CAC7BC,OAAQ,SACRC,SAAU,WACVC,SAAU,WACVC,UAAW,YACXC,IAAK,OAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAgCrDC,EAAiB,IAK5BF,EAAMG,WAAWJ,GCtCJ,IAAAK,EAAA,CACbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAMrC,EAAS,CACbsC,cAAeH,EAAQI,gBAGzB,IAAK,MAAMlD,KAAO6C,EAChBM,OAAOC,eAAezC,EAAQX,EAAK,CACjCO,IAAK,KACH,MAAM8C,EAAOrD,EAOb,OALI8C,EAAQQ,gBAAgBD,KAAUrB,EAAgBK,MACpDS,EAAQQ,gBAAgBD,IAASL,GAAUhB,EAAgBK,KAG7DU,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,EAAK,IAK5B,OAAO1C,CAAM,ECxBf4C,EAAgBzF,GACdI,EAASJ,KAAWqF,OAAOK,KAAK1F,GAAOyD,OCK1BkC,EAAA,CACbC,EACAJ,EACAK,EACAX,KAEAW,EAAgBD,GAChB,MAAM/E,KAAEA,KAASkE,GAAca,EAE/B,OACEH,EAAcV,IACdM,OAAOK,KAAKX,GAAWtB,QAAU4B,OAAOK,KAAKF,GAAiB/B,QAC9D4B,OAAOK,KAAKX,GAAWe,MACpB5D,GACCsD,EAAgBtD,OACdgD,GAAUhB,EAAgBK,MAEhC,EC3BJwB,EAAmB/F,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCElDgG,EAAA,CACbnF,EACAoF,EACAC,KAECrF,IACAoF,GACDpF,IAASoF,GACTF,EAAsBlF,GAAMsF,MACzBC,GACCA,IACCF,EACGE,IAAgBH,EAChBG,EAAYC,WAAWJ,IACvBA,EAAWI,WAAWD,MCN1B,SAAUE,EAAgBC,GAC9B,MAAMC,EAAS9B,EAAM+B,OAAOF,GAC5BC,EAAOE,QAAUH,EAEjB7B,EAAMiC,WAAU,KACd,MAAMC,GACHL,EAAMM,UACPL,EAAOE,QAAQI,SACfN,EAAOE,QAAQI,QAAQC,UAAU,CAC/BC,KAAMR,EAAOE,QAAQM,OAGzB,MAAO,KACLJ,GAAgBA,EAAaK,aAAa,CAC3C,GACA,CAACV,EAAMM,UACZ,CCmBA,SAASK,EACPX,GAEA,MAAMY,EAAUvC,KACVI,QAAEA,EAAUmC,EAAQnC,QAAO6B,SAAEA,EAAQhG,KAAEA,EAAIqF,MAAEA,GAAUK,GAAS,IAC/DxB,EAAWc,GAAmBnB,EAAM0C,SAASpC,EAAQqC,YACtDC,EAAW5C,EAAM+B,QAAO,GACxBc,EAAuB7C,EAAM+B,OAAO,CACxCe,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAEJC,EAAQtD,EAAM+B,OAAO5F,GAoC3B,OAlCAmH,EAAMtB,QAAU7F,EAEhByF,EAAa,CACXO,WACAG,KACEhH,GAEAsH,EAASZ,SACTV,EACEgC,EAAMtB,QACN1G,EAAMa,KACNqF,IAEFP,EACE3F,EACAuH,EAAqBb,QACrB1B,EAAQiD,mBAEVpC,EAAgB,IACXb,EAAQqC,cACRrH,IAEP8G,QAAS9B,EAAQkD,UAAUC,QAG7BzD,EAAMiC,WAAU,KACdW,EAASZ,SAAU,EACnBa,EAAqBb,QAAQoB,SAAW9C,EAAQoD,cAAa,GAEtD,KACLd,EAASZ,SAAU,CAAK,IAEzB,CAAC1B,IAEGF,EACLC,EACAC,EACAuC,EAAqBb,SACrB,EAEJ,CCxGA,IAAA2B,EAAgBrI,GAAqD,iBAAVA,ECI5CsI,EAAA,CACb1H,EACA2H,EACAC,EACAC,EACA7F,IAEIyF,EAASzH,IACX6H,GAAYF,EAAOG,MAAMC,IAAI/H,GACtB6B,EAAI+F,EAAY5H,EAAOgC,IAG5BvC,MAAMC,QAAQM,GACTA,EAAMgI,KACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAAYpG,EAAI+F,EAAYK,OAK/DJ,IAAaF,EAAOO,UAAW,GAExBN,GCqHH,SAAUO,EACdxC,GAEA,MAAMY,EAAUvC,KACVI,QACJA,EAAUmC,EAAQnC,QAAOnE,KACzBA,EAAI+B,aACJA,EAAYiE,SACZA,EAAQX,MACRA,GACEK,GAAS,GACPyB,EAAQtD,EAAM+B,OAAO5F,GAE3BmH,EAAMtB,QAAU7F,EAEhByF,EAAa,CACXO,WACAC,QAAS9B,EAAQkD,UAAUc,OAC3BhC,KAAOjC,IAEHiB,EACEgC,EAAMtB,QACN3B,EAAUlE,KACVqF,IAGF+C,EACErH,EACE0G,EACEN,EAAMtB,QACN1B,EAAQuD,OACRxD,EAAUiE,QAAUhE,EAAQkE,aAC5B,EACAtG,IAIP,IAIL,MAAO5C,EAAOiJ,GAAevE,EAAM0C,SACjCpC,EAAQmE,UACNtI,EACA+B,IAMJ,OAFA8B,EAAMiC,WAAU,IAAM3B,EAAQoE,qBAEvBpJ,CACT,CClJM,SAAUqJ,EAId9C,GAEA,MAAMY,EAAUvC,KACV/D,KAAEA,EAAIgG,SAAEA,EAAQ7B,QAAEA,EAAUmC,EAAQnC,QAAOsE,iBAAEA,GAAqB/C,EAClEgD,EAAe5I,EAAmBqE,EAAQuD,OAAOiB,MAAO3I,GACxDb,EAAQ+I,EAAS,CACrB/D,UACAnE,OACA+B,aAAcH,EACZuC,EAAQkE,YACRrI,EACA4B,EAAIuC,EAAQI,eAAgBvE,EAAM0F,EAAM3D,eAE1CsD,OAAO,IAEHnB,EAAYmC,EAAa,CAC7BlC,UACAnE,OACAqF,OAAO,IAGHuD,EAAiB/E,EAAM+B,OAC3BzB,EAAQ0E,SAAS7I,EAAM,IAClB0F,EAAMoD,MACT3J,WACIgD,EAAUuD,EAAMM,UAAY,CAAEA,SAAUN,EAAMM,UAAa,MAgDnE,OA5CAnC,EAAMiC,WAAU,KACd,MAAMiD,EACJ5E,EAAQ6E,SAASP,kBAAoBA,EAEjCQ,EAAgB,CAACjJ,EAAyBb,KAC9C,MAAM+J,EAAetH,EAAIuC,EAAQgF,QAASnJ,GAEtCkJ,GAASA,EAAME,KACjBF,EAAME,GAAGC,MAAQlK,EAClB,EAKH,GAFA8J,EAAcjJ,GAAM,GAEhB+I,EAAwB,CAC1B,MAAM5J,EAAQ4B,EAAYa,EAAIuC,EAAQ6E,SAAS1E,cAAetE,IAC9DyC,EAAI0B,EAAQI,eAAgBvE,EAAMb,GAC9BsC,EAAYG,EAAIuC,EAAQkE,YAAarI,KACvCyC,EAAI0B,EAAQkE,YAAarI,EAAMb,EAElC,CAED,MAAO,MAEHuJ,EACIK,IAA2B5E,EAAQmF,OAAOC,OAC1CR,GAEF5E,EAAQqF,WAAWxJ,GACnBiJ,EAAcjJ,GAAM,EAAM,CAC/B,GACA,CAACA,EAAMmE,EAASuE,EAAcD,IAEjC5E,EAAMiC,WAAU,KACVlE,EAAIuC,EAAQgF,QAASnJ,IACvBmE,EAAQsF,qBAAqB,CAC3BzD,WACA0D,OAAQvF,EAAQgF,QAChBnJ,OACAb,MAAOyC,EAAIuC,EAAQgF,QAASnJ,GAAMoJ,GAAGjK,OAExC,GACA,CAAC6G,EAAUhG,EAAMmE,IAEb,CACL+E,MAAO,CACLlJ,OACAb,WACIgD,EAAU6D,IAAa9B,EAAU8B,SACjC,CAAEA,SAAU9B,EAAU8B,UAAYA,GAClC,GACJzC,SAAUM,EAAM8F,aACbhK,GACCiJ,EAAe/C,QAAQtC,SAAS,CAC9B3D,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMgE,EAAOG,UAEjB,CAACpD,IAEHsD,OAAQO,EAAM8F,aACZ,IACEf,EAAe/C,QAAQvC,OAAO,CAC5B1D,OAAQ,CACNT,MAAOyC,EAAIuC,EAAQkE,YAAarI,GAChCA,KAAMA,GAERf,KAAMgE,EAAOC,QAEjB,CAAClD,EAAMmE,IAETyF,IAAK/F,EAAM8F,aACRE,IACC,MAAMX,EAAQtH,EAAIuC,EAAQgF,QAASnJ,GAE/BkJ,GAASW,IACXX,EAAME,GAAGQ,IAAM,CACbE,MAAO,IAAMD,EAAIC,QACjBC,OAAQ,IAAMF,EAAIE,SAClBC,kBAAoBC,GAClBJ,EAAIG,kBAAkBC,GACxBC,eAAgB,IAAML,EAAIK,kBAE7B,GAEH,CAAC/F,EAAQgF,QAASnJ,KAGtBkE,YACAiG,WAAY3F,OAAO4F,iBACjB,GACA,CACEC,QAAS,CACPC,YAAY,EACZ1I,IAAK,MAAQA,EAAIsC,EAAUgD,OAAQlH,IAErC2G,QAAS,CACP2D,YAAY,EACZ1I,IAAK,MAAQA,EAAIsC,EAAU2C,YAAa7G,IAE1CuK,UAAW,CACTD,YAAY,EACZ1I,IAAK,MAAQA,EAAIsC,EAAU4C,cAAe9G,IAE5CgH,aAAc,CACZsD,YAAY,EACZ1I,IAAK,MAAQA,EAAIsC,EAAU6C,iBAAkB/G,IAE/CwK,MAAO,CACLF,YAAY,EACZ1I,IAAK,IAAMA,EAAIsC,EAAUgD,OAAQlH,MAK3C,CCzJA,MCzCayK,EAAWC,IACtB,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMtJ,KAAOmD,OAAOK,KAAK6F,GAC5B,GAAIpL,EAAaoL,EAAIrJ,IAAO,CAC1B,MAAMuJ,EAASH,EAAQC,EAAIrJ,IAE3B,IAAK,MAAMwJ,KAAarG,OAAOK,KAAK+F,GAClCD,EAAO,GAAGtJ,KAAOwJ,KAAeD,EAAOC,EAE1C,MACCF,EAAOtJ,GAAOqJ,EAAIrJ,GAItB,OAAOsJ,CAAM,ECbTG,EAAe,OCAN,IAAAC,EAAA,CACb/K,EACAgL,EACA9D,EACAjI,EACAgL,IAEAe,EACI,IACK9D,EAAOlH,GACViL,MAAO,IACD/D,EAAOlH,IAASkH,EAAOlH,GAAOiL,MAAQ/D,EAAOlH,GAAOiL,MAAQ,CAAA,EAChEhM,CAACA,GAAOgL,IAAW,IAGvB,CAAE,ECrBRiB,EAAe,KACb,MAAMC,EACmB,oBAAhBC,YAA8BhM,KAAKiM,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuC7I,QAAQ,SAAU8I,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,GAAG,GACpD,ECLJC,EAAe,CACb3L,EACA0C,EACAkJ,EAAiC,CAAE,IAEnCA,EAAQC,aAAepK,EAAYmK,EAAQC,aACvCD,EAAQE,WACR,GAAG9L,KAAQyB,EAAYmK,EAAQG,YAAcrJ,EAAQkJ,EAAQG,cAC7D,GCRSC,EAACC,IAAsC,CACpDC,YAAaD,GAAQA,IAAS5I,EAAgBG,SAC9C2I,SAAUF,IAAS5I,EAAgBC,OACnC8I,WAAYH,IAAS5I,EAAgBE,SACrC8I,QAASJ,IAAS5I,EAAgBK,IAClC4I,UAAWL,IAAS5I,EAAgBI,YCNvB8I,EAAA,CACbvM,EACA0H,EACA8E,KAECA,IACA9E,EAAOO,UACNP,EAAOG,MAAM5H,IAAID,IACjB,IAAI0H,EAAOG,OAAOvC,MACfmH,GACCzM,EAAKwF,WAAWiH,IAChB,SAASpK,KAAKrC,EAAK0M,MAAMD,EAAU7J,YCT3C,MAAM+J,EAAwB,CAC5BjD,EACAH,EACAqD,EACAC,KAEA,IAAK,MAAMxL,KAAOuL,GAAepI,OAAOK,KAAK6E,GAAS,CACpD,MAAMR,EAAQtH,EAAI8H,EAAQrI,GAE1B,GAAI6H,EAAO,CACT,MAAME,GAAEA,KAAO0D,GAAiB5D,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAG2D,MAAQ3D,EAAG2D,KAAK,IAAMxD,EAAOH,EAAG2D,KAAK,GAAI1L,KAASwL,EACvD,OAAO,EACF,GAAIzD,EAAGQ,KAAOL,EAAOH,EAAGQ,IAAKR,EAAGpJ,QAAU6M,EAC/C,OAAO,EAEP,GAAIF,EAAsBG,EAAcvD,GACtC,KAGL,MAAM,GAAIhK,EAASuN,IACdH,EAAsBG,EAA2BvD,GACnD,KAGL,CACF,CACM,ECvBT,IAAAyD,EAAe,CACb9F,EACAsD,EACAxK,KAEA,MAAMiN,EAAmB/H,EAAsBtD,EAAIsF,EAAQlH,IAG3D,OAFAyC,EAAIwK,EAAkB,OAAQzC,EAAMxK,IACpCyC,EAAIyE,EAAQlH,EAAMiN,GACX/F,CAAM,EChBfgG,EAAgBlO,GACG,SAAjBA,EAAQC,KCHVkO,EAAgBhO,GACG,mBAAVA,ECCMiO,EAACjO,IACd,IAAKwB,EACH,OAAO,EAGT,MAAM0M,EAAQlO,EAAUA,EAAsBmO,cAA6B,EAC3E,OACEnO,aACCkO,GAASA,EAAME,YAAcF,EAAME,YAAY1M,YAAcA,YAC9D,ECRJ2M,EAAgBrO,GAAqCqI,EAASrI,GCD9DsO,GAAgBzO,GACG,UAAjBA,EAAQC,KCHVyO,GAAgBvO,GAAoCA,aAAiBwO,OCOrE,MAAMC,GAAqC,CACzCzO,OAAO,EACP8H,SAAS,GAGL4G,GAAc,CAAE1O,OAAO,EAAM8H,SAAS,GAE7B,IAAA6G,GAAClC,IACd,GAAIpM,MAAMC,QAAQmM,GAAU,CAC1B,GAAIA,EAAQhJ,OAAS,EAAG,CACtB,MAAMuF,EAASyD,EACZrK,QAAQwM,GAAWA,GAAUA,EAAOlO,UAAYkO,EAAO/H,WACvD+B,KAAKgG,GAAWA,EAAO5O,QAC1B,MAAO,CAAEA,MAAOgJ,EAAQlB,UAAWkB,EAAOvF,OAC3C,CAED,OAAOgJ,EAAQ,GAAG/L,UAAY+L,EAAQ,GAAG5F,SAErC4F,EAAQ,GAAGoC,aAAevM,EAAYmK,EAAQ,GAAGoC,WAAW7O,OAC1DsC,EAAYmK,EAAQ,GAAGzM,QAA+B,KAArByM,EAAQ,GAAGzM,MAC1C0O,GACA,CAAE1O,MAAOyM,EAAQ,GAAGzM,MAAO8H,SAAS,GACtC4G,GACFD,EACL,CAED,OAAOA,EAAa,EC5BtB,MAAMK,GAAkC,CACtChH,SAAS,EACT9H,MAAO,MAGM,IAAA+O,GAACtC,GACdpM,MAAMC,QAAQmM,GACVA,EAAQ1J,QACN,CAACiM,EAAUJ,IACTA,GAAUA,EAAOlO,UAAYkO,EAAO/H,SAChC,CACEiB,SAAS,EACT9H,MAAO4O,EAAO5O,OAEhBgP,GACNF,IAEFA,GClBQ,SAAUG,GACtBpM,EACA4H,EACA3K,EAAO,YAEP,GACEuO,EAAUxL,IACTxC,MAAMC,QAAQuC,IAAWA,EAAOqM,MAAMb,IACtCrL,EAAUH,KAAYA,EAEvB,MAAO,CACL/C,OACAgL,QAASuD,EAAUxL,GAAUA,EAAS,GACtC4H,MAGN,CChBA,IAAA0E,GAAgBC,GACdhP,EAASgP,KAAoBb,GAAQa,GACjCA,EACA,CACEpP,MAAOoP,EACPtE,QAAS,ICuBFuE,GAAAC,MACbvF,EACAvB,EACAqD,EACA0D,EACAC,KAEA,MAAM/E,IACJA,EAAGmD,KACHA,EAAI6B,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOC,SACPA,EAAQlP,KACRA,EAAImP,cACJA,EAAa9F,MACbA,EAAKrD,SACLA,GACEkD,EAAME,GACJgG,EAA+BxN,EAAI+F,EAAY3H,GACrD,IAAKqJ,GAASrD,EACZ,MAAO,GAET,MAAMqJ,EAA6BtC,EAAOA,EAAK,GAAMnD,EAC/CI,EAAqBC,IACrByE,GAA6BW,EAASnF,iBACxCmF,EAASrF,kBAAkB7H,EAAU8H,GAAW,GAAKA,GAAW,IAChEoF,EAASnF,iBACV,EAEGM,EAA6B,CAAA,EAC7B8E,EAAU7B,GAAa7D,GACvB2F,EAAaxQ,EAAgB6K,GAC7B4F,EAAoBF,GAAWC,EAC/BE,GACFN,GAAiBjC,EAAYtD,KAC7BnI,EAAYmI,EAAIzK,QAChBsC,EAAY2N,IACbhC,EAAcxD,IAAsB,KAAdA,EAAIzK,OACZ,KAAfiQ,GACC5P,MAAMC,QAAQ2P,KAAgBA,EAAWxM,OACtC8M,EAAoB3E,EAAa4E,KACrC,KACA3P,EACAgL,EACAR,GAEIoF,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBrM,EACnBsM,EAAmBtM,KAEnB,MAAMsG,EAAU4F,EAAYC,EAAmBC,EAC/CvF,EAAMxK,GAAQ,CACZf,KAAM4Q,EAAYG,EAAUC,EAC5BhG,UACAL,SACG8F,EAAkBG,EAAYG,EAAUC,EAAShG,GACrD,EAGH,GACE0E,GACKnP,MAAMC,QAAQ2P,KAAgBA,EAAWxM,OAC1CgM,KACGY,IAAsBC,GAAWpQ,EAAkB+P,KACnDjN,EAAUiN,KAAgBA,GAC1BG,IAAezB,GAAiBf,GAAM9F,SACtCqI,IAAYpB,GAAcnB,GAAM9F,SACvC,CACA,MAAM9H,MAAEA,EAAK8K,QAAEA,GAAYuD,EAAUoB,GACjC,CAAEzP,QAASyP,EAAU3E,QAAS2E,GAC9BN,GAAmBM,GAEvB,GAAIzP,IACFqL,EAAMxK,GAAQ,CACZf,KAAM0E,EACNsG,UACAL,IAAKyF,KACFK,EAAkB/L,EAAiCsG,KAEnDe,GAEH,OADAhB,EAAkBC,GACXO,CAGZ,CAED,KAAKiF,GAAapQ,EAAkB0P,IAAS1P,EAAkB2P,IAAO,CACpE,IAAIa,EACAK,EACJ,MAAMC,EAAY7B,GAAmBU,GAC/BoB,EAAY9B,GAAmBS,GAErC,GAAK1P,EAAkB+P,IAAgBpM,MAAMoM,GAUtC,CACL,MAAMiB,EACHzG,EAAyB0G,aAAe,IAAIlR,KAAKgQ,GAC9CmB,EAAqBC,GACzB,IAAIpR,MAAK,IAAIA,MAAOqR,eAAiB,IAAMD,GACvCE,EAAqB,QAAZ9G,EAAI3K,KACb0R,EAAqB,QAAZ/G,EAAI3K,KAEfuI,EAAS2I,EAAUhR,QAAUiQ,IAC/BS,EAAYa,EACRH,EAAkBnB,GAAcmB,EAAkBJ,EAAUhR,OAC5DwR,EACEvB,EAAae,EAAUhR,MACvBkR,EAAY,IAAIjR,KAAK+Q,EAAUhR,QAGnCqI,EAAS4I,EAAUjR,QAAUiQ,IAC/Bc,EAAYQ,EACRH,EAAkBnB,GAAcmB,EAAkBH,EAAUjR,OAC5DwR,EACEvB,EAAagB,EAAUjR,MACvBkR,EAAY,IAAIjR,KAAKgR,EAAUjR,OAExC,KAjCmE,CAClE,MAAMyR,EACHhH,EAAyBuF,gBACzBC,GAAcA,EAAaA,GACzB/P,EAAkB8Q,EAAUhR,SAC/B0Q,EAAYe,EAAcT,EAAUhR,OAEjCE,EAAkB+Q,EAAUjR,SAC/B+Q,EAAYU,EAAcR,EAAUjR,MAEvC,CAyBD,IAAI0Q,GAAaK,KACfN,IACIC,EACFM,EAAUlG,QACVmG,EAAUnG,QACVtG,EACAA,IAEGqH,GAEH,OADAhB,EAAkBQ,EAAMxK,GAAOiK,SACxBO,CAGZ,CAED,IACGqE,GAAaC,KACbW,IACAjI,EAAS4H,IAAgBT,GAAgBnP,MAAMC,QAAQ2P,IACxD,CACA,MAAMyB,EAAkBvC,GAAmBO,GACrCiC,EAAkBxC,GAAmBQ,GACrCe,GACHxQ,EAAkBwR,EAAgB1R,QACnCiQ,EAAWxM,QAAUiO,EAAgB1R,MACjC+Q,GACH7Q,EAAkByR,EAAgB3R,QACnCiQ,EAAWxM,QAAUkO,EAAgB3R,MAEvC,IAAI0Q,GAAaK,KACfN,EACEC,EACAgB,EAAgB5G,QAChB6G,EAAgB7G,UAEbe,GAEH,OADAhB,EAAkBQ,EAAMxK,GAAOiK,SACxBO,CAGZ,CAED,GAAIyE,IAAYQ,GAAWjI,EAAS4H,GAAa,CAC/C,MAAQjQ,MAAO4R,EAAY9G,QAAEA,GAAYqE,GAAmBW,GAE5D,GAAIvB,GAAQqD,KAAkB3B,EAAW4B,MAAMD,KAC7CvG,EAAMxK,GAAQ,CACZf,KAAM0E,EACNsG,UACAL,SACG8F,EAAkB/L,EAAgCsG,KAElDe,GAEH,OADAhB,EAAkBC,GACXO,CAGZ,CAED,GAAI0E,EACF,GAAI/B,EAAW+B,GAAW,CACxB,MACM+B,EAAgB7C,SADDc,EAASE,EAAYzH,GACK0H,GAE/C,GAAI4B,IACFzG,EAAMxK,GAAQ,IACTiR,KACAvB,EACD/L,EACAsN,EAAchH,WAGbe,GAEH,OADAhB,EAAkBiH,EAAchH,SACzBO,CAGZ,MAAM,GAAIjL,EAAS2P,GAAW,CAC7B,IAAIgC,EAAmB,CAAA,EAEvB,IAAK,MAAM7P,KAAO6N,EAAU,CAC1B,IAAKtK,EAAcsM,KAAsBlG,EACvC,MAGF,MAAMiG,EAAgB7C,SACdc,EAAS7N,GAAK+N,EAAYzH,GAChC0H,EACAhO,GAGE4P,IACFC,EAAmB,IACdD,KACAvB,EAAkBrO,EAAK4P,EAAchH,UAG1CD,EAAkBiH,EAAchH,SAE5Be,IACFR,EAAMxK,GAAQkR,GAGnB,CAED,IAAKtM,EAAcsM,KACjB1G,EAAMxK,GAAQ,CACZ4J,IAAKyF,KACF6B,IAEAlG,GACH,OAAOR,CAGZ,CAIH,OADAR,GAAkB,GACXQ,CAAK,EC1Rd2G,GAAe,CAAInQ,EAAW7B,IAAwB,IACjD6B,KACAkE,EAAsB/F,ICJ3BiS,GAAmBjS,GACjBK,MAAMC,QAAQN,GAASA,EAAM4I,KAAI,KAAe,SAAIpG,ECO9B,SAAA0P,GACtBrQ,EACA0B,EACAvD,GAEA,MAAO,IACF6B,EAAK0L,MAAM,EAAGhK,MACdwC,EAAsB/F,MACtB6B,EAAK0L,MAAMhK,GAElB,CChBA,IAAA4O,GAAe,CACbtQ,EACAuQ,EACAC,IAEKhS,MAAMC,QAAQuB,IAIfS,EAAYT,EAAKwQ,MACnBxQ,EAAKwQ,QAAM7P,GAEbX,EAAKyQ,OAAOD,EAAI,EAAGxQ,EAAKyQ,OAAOF,EAAM,GAAG,IAEjCvQ,GARE,GCNX0Q,GAAe,CAAI1Q,EAAW7B,IAAwB,IACjD+F,EAAsB/F,MACtB+F,EAAsBlE,ICYZ,IAAA2Q,GAAA,CAAI3Q,EAAW0B,IAC5BjB,EAAYiB,GACR,GAdN,SAA4B1B,EAAW4Q,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAI9Q,GAEjB,IAAK,MAAM0B,KAASkP,EAClBE,EAAKL,OAAO/O,EAAQmP,EAAG,GACvBA,IAGF,OAAOvQ,EAAQwQ,GAAMlP,OAASkP,EAAO,EACvC,CAKMC,CACE/Q,EACCkE,EAAsBxC,GAAoBsP,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KCrBtEC,GAAe,CAAInR,EAAWoR,EAAgBC,MAC3CrR,EAAKoR,GAASpR,EAAKqR,IAAW,CAACrR,EAAKqR,GAASrR,EAAKoR,GAAQ,ECyB/C,SAAUE,GAAMzQ,EAAaC,GACzC,MAAMyQ,EAAQ/S,MAAMC,QAAQqC,GACxBA,EACAM,EAAMN,GACJ,CAACA,GACDQ,EAAaR,GAEb0Q,EAA+B,IAAjBD,EAAM3P,OAAef,EA3B3C,SAAiBA,EAAa4Q,GAC5B,MAAM7P,EAAS6P,EAAW/F,MAAM,GAAI,GAAG9J,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbf,EAASJ,EAAYI,GAAUa,IAAUb,EAAO4Q,EAAW/P,MAG7D,OAAOb,CACT,CAkBoD6Q,CAAQ7Q,EAAQ0Q,GAE5D7P,EAAQ6P,EAAM3P,OAAS,EACvBvB,EAAMkR,EAAM7P,GAclB,OAZI8P,UACKA,EAAYnR,GAIT,IAAVqB,IACEnD,EAASiT,IAAgB5N,EAAc4N,IACtChT,MAAMC,QAAQ+S,IA5BrB,SAAsB9H,GACpB,IAAK,MAAMrJ,KAAOqJ,EAChB,GAAIA,EAAIhK,eAAeW,KAASI,EAAYiJ,EAAIrJ,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqCsR,CAAaH,KAE9CF,GAAMzQ,EAAQ0Q,EAAM7F,MAAM,GAAI,IAGzB7K,CACT,CCnDA,IAAA+Q,GAAe,CAAIC,EAAkBnQ,EAAevD,KAClD0T,EAAYnQ,GAASvD,EACd0T,GCcT,IAAAC,GAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,CACR,EACD5M,KAvBYhH,IACZ,IAAK,MAAM8T,KAAYF,EACrBE,EAAS9M,MAAQ8M,EAAS9M,KAAKhH,EAChC,EAqBD+G,UAlBiB+M,IACjBF,EAAWG,KAAKD,GACT,CACL7M,YAAa,KACX2M,EAAaA,EAAWxR,QAAQ4R,GAAMA,IAAMF,GAAS,IAezD7M,YAVkB,KAClB2M,EAAa,EAAE,EAUhB,ECxCHK,GAAgBjU,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUkU,GAAUC,EAAcC,GAC9C,GAAIH,GAAYE,IAAYF,GAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAIrU,EAAaoU,IAAYpU,EAAaqU,GACxC,OAAOD,EAAQE,YAAcD,EAAQC,UAGvC,MAAMC,EAAQjP,OAAOK,KAAKyO,GACpBI,EAAQlP,OAAOK,KAAK0O,GAE1B,GAAIE,EAAM7Q,SAAW8Q,EAAM9Q,OACzB,OAAO,EAGT,IAAK,MAAMvB,KAAOoS,EAAO,CACvB,MAAME,EAAOL,EAAQjS,GAErB,IAAKqS,EAAME,SAASvS,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAMwS,EAAON,EAAQlS,GAErB,GACGnC,EAAayU,IAASzU,EAAa2U,IACnCtU,EAASoU,IAASpU,EAASsU,IAC3BrU,MAAMC,QAAQkU,IAASnU,MAAMC,QAAQoU,IACjCR,GAAUM,EAAME,GACjBF,IAASE,EAEb,OAAO,CAEV,CACF,CAED,OAAO,CACT,CC1Ce,IAAAC,GAAC9U,GACG,oBAAjBA,EAAQC,KCEKuQ,GAAC5F,GACd6D,GAAa7D,IAAQ7K,EAAgB6K,GCFxBmK,GAACnK,GAAawD,EAAcxD,IAAQA,EAAIoK,YCFxCC,GAAIjT,IACjB,IAAK,MAAMK,KAAOL,EAChB,GAAImM,EAAWnM,EAAKK,IAClB,OAAO,EAGX,OAAO,CAAK,ECDd,SAAS6S,GAAmBlT,EAAS0I,EAA8B,IACjE,MAAMyK,EAAoB3U,MAAMC,QAAQuB,GAExC,GAAIzB,EAASyB,IAASmT,EACpB,IAAK,MAAM9S,KAAOL,EAEdxB,MAAMC,QAAQuB,EAAKK,KAClB9B,EAASyB,EAAKK,MAAU4S,GAAkBjT,EAAKK,KAEhDqI,EAAOrI,GAAO7B,MAAMC,QAAQuB,EAAKK,IAAQ,GAAK,GAC9C6S,GAAgBlT,EAAKK,GAAMqI,EAAOrI,KACxBhC,EAAkB2B,EAAKK,MACjCqI,EAAOrI,IAAO,GAKpB,OAAOqI,CACT,CAEA,SAAS0K,GACPpT,EACA2G,EACA0M,GAKA,MAAMF,EAAoB3U,MAAMC,QAAQuB,GAExC,GAAIzB,EAASyB,IAASmT,EACpB,IAAK,MAAM9S,KAAOL,EAEdxB,MAAMC,QAAQuB,EAAKK,KAClB9B,EAASyB,EAAKK,MAAU4S,GAAkBjT,EAAKK,IAG9CI,EAAYkG,IACZyL,GAAYiB,EAAsBhT,IAElCgT,EAAsBhT,GAAO7B,MAAMC,QAAQuB,EAAKK,IAC5C6S,GAAgBlT,EAAKK,GAAM,IAC3B,IAAK6S,GAAgBlT,EAAKK,KAE9B+S,GACEpT,EAAKK,GACLhC,EAAkBsI,GAAc,GAAKA,EAAWtG,GAChDgT,EAAsBhT,IAI1BgT,EAAsBhT,IAAQgS,GAAUrS,EAAKK,GAAMsG,EAAWtG,IAKpE,OAAOgT,CACT,CAEA,IAAAC,GAAe,CAAIhQ,EAAkBqD,IACnCyM,GACE9P,EACAqD,EACAuM,GAAgBvM,IClEpB4M,GAAe,CACbpV,GACEgQ,gBAAemB,cAAakE,gBAE9B/S,EAAYtC,GACRA,EACAgQ,EACY,KAAVhQ,EACEsV,IACAtV,GACGA,EACDA,EACJmR,GAAe9I,EAASrI,GACtB,IAAIC,KAAKD,GACTqV,EACEA,EAAWrV,GACXA,ECTY,SAAAuV,GAActL,GACpC,MAAMQ,EAAMR,EAAGQ,IAEf,KAAIR,EAAG2D,KAAO3D,EAAG2D,KAAKsB,OAAOzE,GAAQA,EAAI5D,WAAY4D,EAAI5D,UAIzD,OAAIkH,EAAYtD,GACPA,EAAI+K,MAGTlH,GAAa7D,GACRsE,GAAc9E,EAAG2D,MAAM5N,MAG5B2U,GAAiBlK,GACZ,IAAIA,EAAIgL,iBAAiB7M,KAAI,EAAG5I,WAAYA,IAGjDoQ,EAAW3F,GACNkE,GAAiB1E,EAAG2D,MAAM5N,MAG5BoV,GAAgB9S,EAAYmI,EAAIzK,OAASiK,EAAGQ,IAAIzK,MAAQyK,EAAIzK,MAAOiK,EAC5E,CCxBe,IAAAyL,GAAA,CACbjI,EACAzD,EACA2L,EACApG,KAEA,MAAMhF,EAAiD,CAAA,EAEvD,IAAK,MAAM1J,KAAQ4M,EAAa,CAC9B,MAAM1D,EAAetH,EAAIuH,EAASnJ,GAElCkJ,GAASzG,EAAIiH,EAAQ1J,EAAMkJ,EAAME,GAClC,CAED,MAAO,CACL0L,eACA/U,MAAO,IAAI6M,GACXlD,SACAgF,4BACD,ECrBHqG,GACEC,GAEAvT,EAAYuT,GACRA,EACAtH,GAAQsH,GACNA,EAAKC,OACL1V,EAASyV,GACPtH,GAAQsH,EAAK7V,OACX6V,EAAK7V,MAAM8V,OACXD,EAAK7V,MACP6V,EChBV,MAAME,GAAiB,gBAER,IAAAC,GAACC,KACZA,GAAmBA,EAAelG,YAEjC/B,EAAWiI,EAAelG,WACzBkG,EAAelG,SAAS1O,YAAYR,OAASkV,IAC9C3V,EAAS6V,EAAelG,WACvB1K,OAAO2D,OAAOiN,EAAelG,UAAUjK,MACpCoQ,GACCA,EAAiB7U,YAAYR,OAASkV,OCZhDI,GAAgB1J,GACdA,EAAQvC,QACPuC,EAAQgD,UACPhD,EAAQmD,KACRnD,EAAQoD,KACRpD,EAAQiD,WACRjD,EAAQkD,WACRlD,EAAQqD,SACRrD,EAAQsD,UCNY,SAAAqG,GACtBrO,EACAiC,EACAnJ,GAKA,MAAMwK,EAAQ5I,EAAIsF,EAAQlH,GAE1B,GAAIwK,GAASpI,EAAMpC,GACjB,MAAO,CACLwK,QACAxK,QAIJ,MAAMD,EAAQC,EAAKiC,MAAM,KAEzB,KAAOlC,EAAM6C,QAAQ,CACnB,MAAMoF,EAAYjI,EAAMyV,KAAK,KACvBtM,EAAQtH,EAAIuH,EAASnB,GACrByN,EAAa7T,EAAIsF,EAAQc,GAE/B,GAAIkB,IAAU1J,MAAMC,QAAQyJ,IAAUlJ,IAASgI,EAC7C,MAAO,CAAEhI,QAGX,GAAIyV,GAAcA,EAAWxW,KAC3B,MAAO,CACLe,KAAMgI,EACNwC,MAAOiL,GAIX1V,EAAM2V,KACP,CAED,MAAO,CACL1V,OAEJ,CC3Ce,IAAA2V,GAAA,CACbnJ,EACAjC,EACAqL,EACAC,EAIA5J,KAEIA,EAAKI,WAEGuJ,GAAe3J,EAAKK,YACrB/B,GAAaiC,IACboJ,EAAcC,EAAe1J,SAAWF,EAAKE,WAC9CK,IACCoJ,EAAcC,EAAezJ,WAAaH,EAAKG,aACjDI,GCfIsJ,GAAA,CAAIlM,EAAQ5J,KACxBsB,EAAQM,EAAIgI,EAAK5J,IAAO4C,QAAU0P,GAAM1I,EAAK5J,GCkFhD,MAAM+V,GAAiB,CACrB9J,KAAM5I,EAAgBG,SACtBqS,eAAgBxS,EAAgBE,SAChCyS,kBAAkB,GAGJ,SAAAC,GAIdvQ,EAA8C,IAE9C,IAsCIwQ,EAtCAlN,EAAW,IACV+M,MACArQ,GAEDc,EAAsC,CACxC2P,YAAa,EACbxP,SAAS,EACTC,UAAWuG,EAAWnE,EAAS1E,eAC/B0C,cAAc,EACd4O,aAAa,EACbQ,cAAc,EACdC,oBAAoB,EACpBpP,SAAS,EACTH,cAAe,CAAE,EACjBD,YAAa,CAAE,EACfE,iBAAkB,CAAE,EACpBG,OAAQ8B,EAAS9B,QAAU,CAAE,EAC7BlB,SAAUgD,EAAShD,WAAY,GAE7BmD,EAAqB,CAAA,EACrB5E,GACFhF,EAASyJ,EAAS1E,gBAAkB/E,EAASyJ,EAASb,UAClDpH,EAAYiI,EAAS1E,eAAiB0E,EAASb,SAC/C,GACFE,EAAcW,EAASP,iBACvB,CAAE,EACF1H,EAAYwD,GACZ+E,EAAS,CACXC,QAAQ,EACRF,OAAO,EACPxB,OAAO,GAELH,EAAgB,CAClB2B,MAAO,IAAInI,IACXoV,QAAS,IAAIpV,IACbyH,MAAO,IAAIzH,IACX2G,MAAO,IAAI3G,KAGTqV,EAAQ,EACZ,MAAM5R,EAAiC,CACrCgC,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEJG,EAAoC,CACxCc,OAAQ2K,KACRnK,MAAOmK,KACPxL,MAAOwL,MAEH0D,EAA6BxK,EAAmBhD,EAASiD,MACzDwK,EAA4BzK,EAAmBhD,EAAS6M,gBACxDa,EACJ1N,EAAS8L,eAAiBzR,EAAgBK,IAStC6D,EAAekH,MAAOkI,IAC1B,IAAKjR,EAAMM,WAAarB,EAAgBsC,SAAW0P,GAAoB,CACrE,MAAM1P,EAAU+B,EAAS4N,SACrBhS,SAAqBiS,KAAkB3P,cACjC4P,EAAyB3N,GAAS,GAExClC,IAAYT,EAAWS,SACzBI,EAAUC,MAAMnB,KAAK,CACnBc,WAGL,GAGG8P,EAAsB,CAAChX,EAAkBiH,KAE1CtB,EAAMM,WACNrB,EAAgBqC,eAAgBrC,EAAgBoC,oBAEhDhH,GAASP,MAAM+R,KAAK7J,EAAO2B,QAAQ2N,SAAShX,IACvCA,IACFgH,EACIvE,EAAI+D,EAAWO,iBAAkB/G,EAAMgH,GACvCsL,GAAM9L,EAAWO,iBAAkB/G,GACxC,IAGHqH,EAAUC,MAAMnB,KAAK,CACnBY,iBAAkBP,EAAWO,iBAC7BC,cAAepC,EAAc4B,EAAWO,oBAE3C,EA2EGkQ,EAAsB,CAC1BjX,EACAkX,EACA/X,EACAyK,KAEA,MAAMV,EAAetH,EAAIuH,EAASnJ,GAElC,GAAIkJ,EAAO,CACT,MAAMnH,EAAeH,EACnByG,EACArI,EACAyB,EAAYtC,GAASyC,EAAI2C,EAAgBvE,GAAQb,GAGnDsC,EAAYM,IACX6H,GAAQA,EAAyBuN,gBAClCD,EACIzU,EACE4F,EACArI,EACAkX,EAAuBnV,EAAe2S,GAAcxL,EAAME,KAE5DgO,EAAcpX,EAAM+B,GAExBuH,EAAOD,OAAS9B,GACjB,GAGG8P,EAAsB,CAC1BrX,EACAsX,EACA9K,EACA+K,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAM/M,EAA8D,CAClE3K,QAGF,IAAK0F,EAAMM,SAAU,CACnB,MAAM2R,KACJ/V,EAAIuH,EAASnJ,IACb4B,EAAIuH,EAASnJ,GAAMoJ,IACnBxH,EAAIuH,EAASnJ,GAAMoJ,GAAGpD,UAExB,IAAKwG,GAAe+K,EAAa,CAC3B5S,EAAgBgC,UAClB+Q,EAAkBlR,EAAWG,QAC7BH,EAAWG,QAAUgE,EAAOhE,QAAUiR,IACtCH,EAAoBC,IAAoB/M,EAAOhE,SAGjD,MAAMkR,EACJF,GAAiBtE,GAAUzR,EAAI2C,EAAgBvE,GAAOsX,GAExDI,IACGC,IAAiB/V,EAAI4E,EAAWK,YAAa7G,IAEhD6X,GAA0BF,EACtBrF,GAAM9L,EAAWK,YAAa7G,GAC9ByC,EAAI+D,EAAWK,YAAa7G,GAAM,GACtC2K,EAAO9D,YAAcL,EAAWK,YAChC4Q,EACEA,GACC9S,EAAgBkC,aACf6Q,KAAqBG,CAC1B,CAED,GAAIrL,EAAa,CACf,MAAMsL,EAAyBlW,EAAI4E,EAAWM,cAAe9G,GAExD8X,IACHrV,EAAI+D,EAAWM,cAAe9G,EAAMwM,GACpC7B,EAAO7D,cAAgBN,EAAWM,cAClC2Q,EACEA,GACC9S,EAAgBmC,eACfgR,IAA2BtL,EAElC,CAEDiL,GAAqBD,GAAgBnQ,EAAUC,MAAMnB,KAAKwE,EAC3D,CAED,OAAO8M,EAAoB9M,EAAS,EAAE,EAGlCoN,EAAsB,CAC1B/X,EACAiH,EACAuD,EACAL,KAMA,MAAM6N,EAAqBpW,EAAI4E,EAAWU,OAAQlH,GAC5C2W,EACJhS,EAAgBsC,SAChB9E,EAAU8E,IACVT,EAAWS,UAAYA,EA1NzB,IAAqBgR,EAuOrB,GAXIvS,EAAMwS,YAAc1N,GA5NHyN,EA6NW,IA5Hb,EAACjY,EAAyBwK,KAC7C/H,EAAI+D,EAAWU,OAAQlH,EAAMwK,GAC7BnD,EAAUC,MAAMnB,KAAK,CACnBe,OAAQV,EAAWU,QACnB,EAwHoCiR,CAAanY,EAAMwK,GAAvD0L,EA5NDkC,IACCC,aAAa9B,GACbA,EAAQ+B,WAAWL,EAAUG,EAAK,EA2NlClC,EAAmBxQ,EAAMwS,cAEzBG,aAAa9B,GACbL,EAAqB,KACrB1L,EACI/H,EAAI+D,EAAWU,OAAQlH,EAAMwK,GAC7B8H,GAAM9L,EAAWU,OAAQlH,KAI5BwK,GAAS6I,GAAU2E,EAAoBxN,GAASwN,KAChDpT,EAAcuF,IACfwM,EACA,CACA,MAAM4B,EAAmB,IACpBpO,KACCwM,GAAqBxU,EAAU8E,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnBlH,QAGFwG,EAAa,IACRA,KACA+R,GAGLlR,EAAUC,MAAMnB,KAAKoS,EACtB,GAGG1B,EAAiBpI,MAAOzO,IAC5B+W,EAAoB/W,GAAM,GAC1B,MAAMgC,QAAegH,EAAS4N,SAC5BvO,EACAW,EAASwP,QACT3D,GACE7U,GAAQ0H,EAAO2B,MACfF,EACAH,EAAS8L,aACT9L,EAAS0F,4BAIb,OADAqI,EAAoB/W,GACbgC,CAAM,EAoBT8U,EAA2BrI,MAC/B/E,EACA+O,EACAD,EAEI,CACFE,OAAO,MAGT,IAAK,MAAM1Y,KAAQ0J,EAAQ,CACzB,MAAMR,EAAQQ,EAAO1J,GAErB,GAAIkJ,EAAO,CACT,MAAME,GAAEA,KAAOkO,GAAepO,EAE9B,GAAIE,EAAI,CACN,MAAMuP,EAAmBjR,EAAOiB,MAAM1I,IAAImJ,EAAGpJ,MACvC4Y,EACJ1P,EAAME,IAAM+L,GAAsBjM,EAAgBE,IAEhDwP,GAAqBjU,EAAgBoC,kBACvCgQ,EAAoB,CAAC/W,IAAO,GAG9B,MAAM6Y,QAAmBrK,GACvBtF,EACAb,EACAqO,EACA1N,EAAS0F,4BAA8B+J,EACvCE,GAOF,GAJIC,GAAqBjU,EAAgBoC,kBACvCgQ,EAAoB,CAAC/W,IAGnB6Y,EAAWzP,EAAGpJ,QAChBwY,EAAQE,OAAQ,EACZD,GACF,OAIHA,IACE7W,EAAIiX,EAAYzP,EAAGpJ,MAChB2Y,EACE3L,EACExG,EAAWU,OACX2R,EACAzP,EAAGpJ,MAELyC,EAAI+D,EAAWU,OAAQkC,EAAGpJ,KAAM6Y,EAAWzP,EAAGpJ,OAChDsS,GAAM9L,EAAWU,OAAQkC,EAAGpJ,MACnC,EAEA4E,EAAc0S,UACNR,EACLQ,EACAmB,EACAD,EAEL,CACF,CAED,OAAOA,EAAQE,KAAK,EAiBhBd,EAAwB,CAAC5X,EAAMgB,KAClC0E,EAAMM,WACNhG,GAAQgB,GAAQyB,EAAI4F,EAAarI,EAAMgB,IACvCqS,GAAUyF,KAAavU,IAEpB+D,EAAyC,CAC7CvI,EACAgC,EACA6F,IAEAH,EACE1H,EACA2H,EACA,IACM4B,EAAOD,MACPhB,EACA5G,EAAYM,GACVwC,EACAiD,EAASzH,GACP,CAAEA,CAACA,GAAQgC,GACXA,GAEV6F,EACA7F,GAcEqV,EAAgB,CACpBpX,EACAb,EACAyM,EAA0B,CAAA,KAE1B,MAAM1C,EAAetH,EAAIuH,EAASnJ,GAClC,IAAIsX,EAAsBnY,EAE1B,GAAI+J,EAAO,CACT,MAAMkM,EAAiBlM,EAAME,GAEzBgM,KACDA,EAAepP,UACdvD,EAAI4F,EAAarI,EAAMuU,GAAgBpV,EAAOiW,IAEhDkC,EACElK,EAAcgI,EAAexL,MAAQvK,EAAkBF,GACnD,GACAA,EAEF2U,GAAiBsB,EAAexL,KAClC,IAAIwL,EAAexL,IAAIgC,SAASoL,SAC7B+B,GACEA,EAAUC,SACT1B,EACA1D,SAASmF,EAAU5Z,SAEhBiW,EAAerI,KACpBhO,EAAgBqW,EAAexL,KACjCwL,EAAerI,KAAKnK,OAAS,EACzBwS,EAAerI,KAAKiK,SACjBiC,KACGA,EAAY9B,iBAAmB8B,EAAYjT,YAC5CiT,EAAYpZ,QAAUL,MAAMC,QAAQ6X,KAC9BA,EAAkBrS,MAClBjE,GAAiBA,IAASiY,EAAY9Z,QAEzCmY,IAAe2B,EAAY9Z,SAEnCiW,EAAerI,KAAK,KACnBqI,EAAerI,KAAK,GAAGlN,UAAYyX,GAExClC,EAAerI,KAAKiK,SACjBkC,GACEA,EAASrZ,QAAUqZ,EAAS/Z,QAAUmY,IAGpCpK,EAAYkI,EAAexL,KACpCwL,EAAexL,IAAIzK,MAAQ,IAE3BiW,EAAexL,IAAIzK,MAAQmY,EAEtBlC,EAAexL,IAAI3K,MACtBoI,EAAUc,OAAOhC,KAAK,CACpBnG,OACAmI,OAAQ,IAAKE,MAKtB,EAEAuD,EAAQ2L,aAAe3L,EAAQuN,cAC9B9B,EACErX,EACAsX,EACA1L,EAAQuN,YACRvN,EAAQ2L,aACR,GAGJ3L,EAAQwN,gBAAkBC,GAAQrZ,EAA2B,EAGzDsZ,EAAY,CAKhBtZ,EACAb,EACAyM,KAEA,IAAK,MAAM2N,KAAYpa,EAAO,CAC5B,MAAMmY,EAAanY,EAAMoa,GACnBvR,EAAY,GAAGhI,KAAQuZ,IACvBrQ,EAAQtH,EAAIuH,EAASnB,IAE1BN,EAAOiB,MAAM1I,IAAID,IAChBT,EAAS+X,IACRpO,IAAUA,EAAME,MAClBlK,EAAaoY,GACVgC,EAAUtR,EAAWsP,EAAY1L,GACjCwL,EAAcpP,EAAWsP,EAAY1L,EAC1C,GAGG4N,EAA0C,CAC9CxZ,EACAb,EACAyM,EAAU,CAAA,KAEV,MAAM1C,EAAQtH,EAAIuH,EAASnJ,GACrB2O,EAAejH,EAAOiB,MAAM1I,IAAID,GAChCyZ,EAAa1Y,EAAY5B,GAE/BsD,EAAI4F,EAAarI,EAAMyZ,GAEnB9K,GACFtH,EAAUsB,MAAMxC,KAAK,CACnBnG,OACAmI,OAAQ,IAAKE,MAIZ1D,EAAgBgC,SAAWhC,EAAgBkC,cAC5C+E,EAAQ2L,aAERlQ,EAAUC,MAAMnB,KAAK,CACnBnG,OACA6G,YAAayN,GAAe/P,EAAgB8D,GAC5C1B,QAASiR,EAAU5X,EAAMyZ,OAI7BvQ,GAAUA,EAAME,IAAO/J,EAAkBoa,GAErCrC,EAAcpX,EAAMyZ,EAAY7N,GADhC0N,EAAUtZ,EAAMyZ,EAAY7N,GAIlCW,EAAUvM,EAAM0H,IAAWL,EAAUC,MAAMnB,KAAK,IAAKK,IACrDa,EAAUc,OAAOhC,KAAK,CACpBnG,KAAMsJ,EAAOD,MAAQrJ,OAAO2B,EAC5BwG,OAAQ,IAAKE,IACb,EAGE9E,EAA0BkL,MAAO9O,IACrC2J,EAAOD,OAAQ,EACf,MAAMzJ,EAASD,EAAMC,OACrB,IAAII,EAAOJ,EAAOI,KACd0Z,GAAsB,EAC1B,MAAMxQ,EAAetH,EAAIuH,EAASnJ,GAG5B2Z,EAA8BrC,IAClCoC,EACEE,OAAO5W,MAAMsU,IACZpY,EAAaoY,IAAetU,MAAMsU,EAAW9D,YAC9CH,GAAUiE,EAAY1V,EAAIyG,EAAarI,EAAMsX,GAAY,EAG7D,GAAIpO,EAAO,CACT,IAAIsB,EACAvD,EACJ,MAAMqQ,EAXN1X,EAAOX,KAAOyV,GAAcxL,EAAME,IAAM1J,EAAcC,GAYhD6M,EACJ7M,EAAMV,OAASgE,EAAOC,MAAQvD,EAAMV,OAASgE,EAAOE,UAChD0W,GACFvE,GAAcpM,EAAME,MACnBJ,EAAS4N,WACThV,EAAI4E,EAAWU,OAAQlH,KACvBkJ,EAAME,GAAG0Q,MACZnE,GACEnJ,EACA5K,EAAI4E,EAAWM,cAAe9G,GAC9BwG,EAAWoP,YACXa,EACAD,GAEEuD,EAAUxN,EAAUvM,EAAM0H,EAAQ8E,GAExC/J,EAAI4F,EAAarI,EAAMsX,GAEnB9K,GACFtD,EAAME,GAAG9F,QAAU4F,EAAME,GAAG9F,OAAO3D,GACnCuW,GAAsBA,EAAmB,IAChChN,EAAME,GAAG7F,UAClB2F,EAAME,GAAG7F,SAAS5D,GAGpB,MAAMwK,EAAakN,EACjBrX,EACAsX,EACA9K,GACA,GAGIgL,GAAgB5S,EAAcuF,IAAe4P,EASnD,IAPCvN,GACCnF,EAAUc,OAAOhC,KAAK,CACpBnG,OACAf,KAAMU,EAAMV,KACZkJ,OAAQ,IAAKE,KAGbwR,EAWF,OAVIlV,EAAgBsC,UACC,WAAfvB,EAAMuG,KACJO,GACFjF,IAGFA,KAKFiQ,GACAnQ,EAAUC,MAAMnB,KAAK,CAAEnG,UAAU+Z,EAAU,CAAE,EAAG5P,IAMpD,IAFCqC,GAAeuN,GAAW1S,EAAUC,MAAMnB,KAAK,IAAKK,IAEjDwC,EAAS4N,SAAU,CACrB,MAAM1P,OAAEA,SAAiB2P,EAAe,CAAC7W,IAIzC,GAFA2Z,EAA2BrC,GAEvBoC,EAAqB,CACvB,MAAMM,EAA4BzE,GAChC/O,EAAWU,OACXiC,EACAnJ,GAEIia,EAAoB1E,GACxBrO,EACAiC,EACA6Q,EAA0Bha,MAAQA,GAGpCwK,EAAQyP,EAAkBzP,MAC1BxK,EAAOia,EAAkBja,KAEzBiH,EAAUrC,EAAcsC,EACzB,CACF,MACC6P,EAAoB,CAAC/W,IAAO,GAC5BwK,SACQgE,GACJtF,EACAb,EACAqO,EACA1N,EAAS0F,4BAEX1O,GACF+W,EAAoB,CAAC/W,IAErB2Z,EAA2BrC,GAEvBoC,IACElP,EACFvD,GAAU,EACDtC,EAAgBsC,UACzBA,QAAgB6P,EAAyB3N,GAAS,KAKpDuQ,IACFxQ,EAAME,GAAG0Q,MACPT,GACEnQ,EAAME,GAAG0Q,MAIb/B,EAAoB/X,EAAMiH,EAASuD,EAAOL,GAE7C,GAGG+P,GAAc,CAACtQ,EAAUvI,KAC7B,GAAIO,EAAI4E,EAAWU,OAAQ7F,IAAQuI,EAAIE,MAErC,OADAF,EAAIE,QACG,CAEF,EAGHuP,GAAwC5K,MAAOzO,EAAM4L,EAAU,CAAA,KACnE,IAAI3E,EACAiK,EACJ,MAAMiJ,EAAajV,EAAsBlF,GAEzC,GAAIgJ,EAAS4N,SAAU,CACrB,MAAM1P,OAta0BuH,OAAO1O,IACzC,MAAMmH,OAAEA,SAAiB2P,EAAe9W,GAExC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAMyK,EAAQ5I,EAAIsF,EAAQlH,GAC1BwK,EACI/H,EAAI+D,EAAWU,OAAQlH,EAAMwK,GAC7B8H,GAAM9L,EAAWU,OAAQlH,EAC9B,MAEDwG,EAAWU,OAASA,EAGtB,OAAOA,CAAM,EAwZUkT,CACnB3Y,EAAYzB,GAAQA,EAAOma,GAG7BlT,EAAUrC,EAAcsC,GACxBgK,EAAmBlR,GACdma,EAAW7U,MAAMtF,GAAS4B,EAAIsF,EAAQlH,KACvCiH,CACL,MAAUjH,GACTkR,SACQmJ,QAAQ3W,IACZyW,EAAWpS,KAAI0G,MAAOzG,IACpB,MAAMkB,EAAQtH,EAAIuH,EAASnB,GAC3B,aAAa8O,EACX5N,GAASA,EAAME,GAAK,CAAEpB,CAACA,GAAYkB,GAAUA,EAC9C,MAGLmF,MAAM7M,UACL0P,GAAqB1K,EAAWS,UAAYM,KAE/C2J,EAAmBjK,QAAgB6P,EAAyB3N,GAoB9D,OAjBA9B,EAAUC,MAAMnB,KAAK,KACdqB,EAASxH,IACb2E,EAAgBsC,SAAWA,IAAYT,EAAWS,QAC/C,CAAE,EACF,CAAEjH,WACFgJ,EAAS4N,WAAa5W,EAAO,CAAEiH,WAAY,GAC/CC,OAAQV,EAAWU,SAGrB0E,EAAQC,cACLqF,GACDvE,EACExD,EACA+Q,GACAla,EAAOma,EAAazS,EAAO2B,OAGxB6H,CAAgB,EAGnB4H,GACJqB,IAIA,MAAMhS,EAAS,IACTmB,EAAOD,MAAQhB,EAAc9D,GAGnC,OAAO9C,EAAY0Y,GACfhS,EACAX,EAAS2S,GACPvY,EAAIuG,EAAQgS,GACZA,EAAWpS,KAAK/H,GAAS4B,EAAIuG,EAAQnI,IAAM,EAG7Csa,GAAoD,CACxDta,EACAkE,KACI,CACJmG,UAAWzI,GAAKsC,GAAasC,GAAYU,OAAQlH,GACjD2G,UAAW/E,GAAKsC,GAAasC,GAAYK,YAAa7G,GACtDwK,MAAO5I,GAAKsC,GAAasC,GAAYU,OAAQlH,GAC7CgH,eAAgBpF,EAAI4E,EAAWO,iBAAkB/G,GACjDuK,YAAa3I,GAAKsC,GAAasC,GAAYM,cAAe9G,KActDua,GAA0C,CAACva,EAAMwK,EAAOoB,KAC5D,MAAMhC,GAAOhI,EAAIuH,EAASnJ,EAAM,CAAEoJ,GAAI,CAAA,IAAMA,IAAM,CAAA,GAAIQ,IAChD4Q,EAAe5Y,EAAI4E,EAAWU,OAAQlH,IAAS,IAG7C4J,IAAK6Q,EAAUxQ,QAAEA,EAAOhL,KAAEA,KAASyb,GAAoBF,EAE/D/X,EAAI+D,EAAWU,OAAQlH,EAAM,IACxB0a,KACAlQ,EACHZ,QAGFvC,EAAUC,MAAMnB,KAAK,CACnBnG,OACAkH,OAAQV,EAAWU,OACnBD,SAAS,IAGX2E,GAAWA,EAAQC,aAAejC,GAAOA,EAAIE,OAASF,EAAIE,OAAO,EA4B7DN,GAA8C,CAACxJ,EAAM4L,EAAU,CAAA,KACnE,IAAK,MAAM5D,KAAahI,EAAOkF,EAAsBlF,GAAQ0H,EAAO2B,MAClE3B,EAAO2B,MAAMsR,OAAO3S,GACpBN,EAAOiB,MAAMgS,OAAO3S,GAEf4D,EAAQgP,YACXtI,GAAMnJ,EAASnB,GACfsK,GAAMjK,EAAaL,KAGpB4D,EAAQiP,WAAavI,GAAM9L,EAAWU,OAAQc,IAC9C4D,EAAQkP,WAAaxI,GAAM9L,EAAWK,YAAamB,IACnD4D,EAAQmP,aAAezI,GAAM9L,EAAWM,cAAekB,IACvD4D,EAAQoP,kBACP1I,GAAM9L,EAAWO,iBAAkBiB,IACpCgB,EAASP,mBACPmD,EAAQqP,kBACT3I,GAAM/N,EAAgByD,GAG1BX,EAAUc,OAAOhC,KAAK,CACpBgC,OAAQ,IAAKE,KAGfhB,EAAUC,MAAMnB,KAAK,IAChBK,KACEoF,EAAQkP,UAAiB,CAAEnU,QAASiR,KAAhB,CAAA,KAG1BhM,EAAQsP,aAAe3T,GAAc,EAGlCkC,GAAsE,EAC1EzD,WACAhG,OACAkJ,QACAQ,SACAvK,YAEA,GAAKgD,EAAU6D,IAAasD,EAAOD,OAAYrD,EAAU,CACvD,MAAMoJ,EAAapJ,OACfrE,EACAF,EAAYtC,GACVuV,GAAcxL,EAAQA,EAAME,GAAKxH,EAAI8H,EAAQ1J,GAAMoJ,IACnDjK,EACNsD,EAAI4F,EAAarI,EAAMoP,GACvBiI,EAAoBrX,EAAMoP,GAAY,GAAO,GAAO,EACrD,GAGGvG,GAA0C,CAAC7I,EAAM4L,EAAU,CAAA,KAC/D,IAAI1C,EAAQtH,EAAIuH,EAASnJ,GACzB,MAAMmb,EACJhZ,EAAUyJ,EAAQ5F,WAAa7D,EAAUuD,EAAMM,UA0BjD,OAxBAvD,EAAI0G,EAASnJ,EAAM,IACbkJ,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEQ,IAAK,CAAE5J,SAC5CA,OACAqJ,OAAO,KACJuC,KAGPlE,EAAO2B,MAAMvB,IAAI9H,GAEbkJ,EACFO,GAAqB,CACnBP,QACAlD,SAAU7D,EAAUyJ,EAAQ5F,UACxB4F,EAAQ5F,SACRN,EAAMM,SACVhG,OACAb,MAAOyM,EAAQzM,QAGjB8X,EAAoBjX,GAAM,EAAM4L,EAAQzM,OAGnC,IACDgc,EACA,CAAEnV,SAAU4F,EAAQ5F,UAAYN,EAAMM,UACtC,MACAgD,EAASoS,YACT,CACExM,WAAYhD,EAAQgD,SACpBG,IAAKgG,GAAanJ,EAAQmD,KAC1BC,IAAK+F,GAAanJ,EAAQoD,KAC1BF,UAAWiG,GAAqBnJ,EAAQkD,WACxCD,UAAWkG,GAAanJ,EAAQiD,WAChCI,QAAS8F,GAAanJ,EAAQqD,UAEhC,GACJjP,OACAuD,WACAD,OAAQC,EACRqG,IAAMA,IACJ,GAAIA,EAAK,CACPf,GAAS7I,EAAM4L,GACf1C,EAAQtH,EAAIuH,EAASnJ,GAErB,MAAMqb,EAAW5Z,EAAYmI,EAAIzK,QAC7ByK,EAAI0R,kBACD1R,EAAI0R,iBAAiB,yBAAyB,IAEjD1R,EACE2R,EAAkB/L,GAAkB6L,GACpCtO,EAAO7D,EAAME,GAAG2D,MAAQ,GAE9B,GACEwO,EACIxO,EAAK9H,MAAM8I,GAAgBA,IAAWsN,IACtCA,IAAanS,EAAME,GAAGQ,IAE1B,OAGFnH,EAAI0G,EAASnJ,EAAM,CACjBoJ,GAAI,IACCF,EAAME,MACLmS,EACA,CACExO,KAAM,IACDA,EAAKxL,OAAOwS,IACfsH,KACI7b,MAAMC,QAAQmC,EAAI2C,EAAgBvE,IAAS,CAAC,IAAM,IAExD4J,IAAK,CAAE3K,KAAMoc,EAASpc,KAAMe,SAE9B,CAAE4J,IAAKyR,MAIfpE,EAAoBjX,GAAM,OAAO2B,EAAW0Z,EAC7C,MACCnS,EAAQtH,EAAIuH,EAASnJ,EAAM,CAAE,GAEzBkJ,EAAME,KACRF,EAAME,GAAGC,OAAQ,IAGlBL,EAASP,kBAAoBmD,EAAQnD,qBAClC3I,EAAmB4H,EAAOiB,MAAO3I,KAASsJ,EAAOC,SACnD7B,EAAO4O,QAAQxO,IAAI9H,EACtB,EAEJ,EAGGwb,GAAc,IAClBxS,EAASgN,kBACTrJ,EAAsBxD,EAAS+Q,GAAaxS,EAAO2B,OAyB/CoS,GACJ,CAACC,EAASC,IAAclN,MAAOmN,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACtBF,EAAEG,SAAWH,EAAEG,WAEjB,IAAIlJ,EAAc9R,EAAYsH,GAM9B,GAJAhB,EAAUC,MAAMnB,KAAK,CACnBiQ,cAAc,IAGZpN,EAAS4N,SAAU,CACrB,MAAM1P,OAAEA,EAAMiB,OAAEA,SAAiB0O,IACjCrQ,EAAWU,OAASA,EACpB2L,EAAc1K,CACf,YACO2O,EAAyB3N,GAKjC,GAFAmJ,GAAM9L,EAAWU,OAAQ,QAErBtC,EAAc4B,EAAWU,QAAS,CACpCG,EAAUC,MAAMnB,KAAK,CACnBe,OAAQ,CAAE,IAEZ,UACQwU,EAAQ7I,EAA6B+I,EAC5C,CAAC,MAAOpR,GACPqR,EAAerR,CAChB,CACF,MACKmR,SACIA,EAAU,IAAKnV,EAAWU,QAAU0U,GAE5CJ,KACAlD,WAAWkD,IAUb,GAPAnU,EAAUC,MAAMnB,KAAK,CACnByP,aAAa,EACbQ,cAAc,EACdC,mBAAoBzR,EAAc4B,EAAWU,UAAY2U,EACzD1F,YAAa3P,EAAW2P,YAAc,EACtCjP,OAAQV,EAAWU,SAEjB2U,EACF,MAAMA,CACP,EAsCCG,GAAqC,CACzCrU,EACAsU,EAAmB,CAAA,KAEnB,MAAMC,EAAgBvU,EAAa5G,EAAY4G,GAAcpD,EACvD4X,EAAqBpb,EAAYmb,GACjCE,EAAqBxX,EAAc+C,GACnCQ,EAASiU,EAAqB7X,EAAiB4X,EAMrD,GAJKF,EAAiBI,oBACpB9X,EAAiB2X,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAItb,IAAI,IACzBwG,EAAO2B,SACP7E,OAAOK,KAAKyP,GAAe/P,EAAgB8D,MAEhD,IAAK,MAAML,KAAaxI,MAAM+R,KAAKiL,GACjC5a,EAAI4E,EAAWK,YAAamB,GACxBvF,EAAI0F,EAAQH,EAAWpG,EAAIyG,EAAaL,IACxCwR,EACExR,EACApG,EAAIuG,EAAQH,GAGrB,KAAM,CACL,GAAIrH,GAASc,EAAYkG,GACvB,IAAK,MAAM3H,KAAQ0H,EAAO2B,MAAO,CAC/B,MAAMH,EAAQtH,EAAIuH,EAASnJ,GAC3B,GAAIkJ,GAASA,EAAME,GAAI,CACrB,MAAMgM,EAAiB5V,MAAMC,QAAQyJ,EAAME,GAAG2D,MAC1C7D,EAAME,GAAG2D,KAAK,GACd7D,EAAME,GAAGQ,IAEb,GAAIwD,EAAcgI,GAAiB,CACjC,MAAMqH,EAAOrH,EAAesH,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,KACD,CACF,CACF,CACF,CAGHxT,EAAU,CAAA,CACX,CAEDd,EAAc3C,EAAM+C,iBAChBwT,EAAiBI,kBACftb,EAAYwD,GACZ,CAAE,EACJxD,EAAYoH,GAEhBd,EAAUsB,MAAMxC,KAAK,CACnBgC,OAAQ,IAAKA,KAGfd,EAAUc,OAAOhC,KAAK,CACpBgC,OAAQ,IAAKA,IAEhB,CAEDT,EAAS,CACP2B,MAAO4S,EAAiBM,gBAAkB7U,EAAO2B,MAAQ,IAAInI,IAC7DoV,QAAS,IAAIpV,IACbyH,MAAO,IAAIzH,IACX2G,MAAO,IAAI3G,IACX+G,UAAU,EACV6B,MAAO,IAGTR,EAAOD,OACJ1E,EAAgBsC,WACfgV,EAAiBf,eACjBe,EAAiBM,gBAErBjT,EAAOzB,QAAUnC,EAAM+C,iBAEvBpB,EAAUC,MAAMnB,KAAK,CACnBgQ,YAAa8F,EAAiBW,gBAC1BpW,EAAW2P,YACX,EACJxP,SAASyV,IAELH,EAAiBnB,UACftU,EAAWG,WAETsV,EAAiBI,mBAChBhJ,GAAU1L,EAAYpD,KAE/BqR,cAAaqG,EAAiBY,iBAC1BrW,EAAWoP,YAEf/O,YAAauV,EACT,CAAE,EACFH,EAAiBM,gBACfN,EAAiBI,mBAAqBhU,EACpCiM,GAAe/P,EAAgB8D,GAC/B7B,EAAWK,YACboV,EAAiBI,mBAAqB1U,EACpC2M,GAAe/P,EAAgBoD,GAC/BsU,EAAiBnB,UACftU,EAAWK,YACX,CAAE,EACZC,cAAemV,EAAiBlB,YAC5BvU,EAAWM,cACX,CAAE,EACNI,OAAQ+U,EAAiBa,WAAatW,EAAWU,OAAS,CAAE,EAC5DmP,qBAAoB4F,EAAiBc,wBACjCvW,EAAW6P,mBAEfD,cAAc,GACd,EAGEuG,GAAoC,CAAChV,EAAYsU,IACrDD,GACE7O,EAAWxF,GACNA,EAAwBU,GACzBV,EACJsU,GAqCJ,MAAO,CACL9X,QAAS,CACP0E,YACAW,cACA8Q,iBACAmB,gBACAlB,YACA1D,iBACAvO,YACAsP,YACArQ,eACAgB,iBAr4BqB,KACvB,IAAK,MAAMvI,KAAQ0H,EAAO4O,QAAS,CACjC,MAAMpN,EAAetH,EAAIuH,EAASnJ,GAElCkJ,IACGA,EAAME,GAAG2D,KACN7D,EAAME,GAAG2D,KAAKsB,OAAOzE,IAASmK,GAAKnK,MAClCmK,GAAK7K,EAAME,GAAGQ,OACnBJ,GAAWxJ,EACd,CAED0H,EAAO4O,QAAU,IAAIpV,GAAK,EA23BxB8b,kBA9rC6C,CAC/Chd,EACAmI,EAAS,GACT8U,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAWvX,EAAMM,SAAU,CAErC,GADAsD,EAAOC,QAAS,EACZ6T,GAA8B5d,MAAMC,QAAQmC,EAAIuH,EAASnJ,IAAQ,CACnE,MAAM6S,EAAcoK,EAAOrb,EAAIuH,EAASnJ,GAAOkd,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmB1a,EAAI0G,EAASnJ,EAAM6S,EACvC,CAED,GACEuK,GACA5d,MAAMC,QAAQmC,EAAI4E,EAAWU,OAAQlH,IACrC,CACA,MAAMkH,EAAS+V,EACbrb,EAAI4E,EAAWU,OAAQlH,GACvBkd,EAAKG,KACLH,EAAKI,MAEPH,GAAmB1a,EAAI+D,EAAWU,OAAQlH,EAAMkH,GAChD4O,GAAgBtP,EAAWU,OAAQlH,EACpC,CAED,GACE2E,EAAgBmC,eAChBsW,GACA5d,MAAMC,QAAQmC,EAAI4E,EAAWM,cAAe9G,IAC5C,CACA,MAAM8G,EAAgBmW,EACpBrb,EAAI4E,EAAWM,cAAe9G,GAC9Bkd,EAAKG,KACLH,EAAKI,MAEPH,GAAmB1a,EAAI+D,EAAWM,cAAe9G,EAAM8G,EACxD,CAEGnC,EAAgBkC,cAClBL,EAAWK,YAAcyN,GAAe/P,EAAgB8D,IAG1DhB,EAAUC,MAAMnB,KAAK,CACnBnG,OACA2G,QAASiR,EAAU5X,EAAMmI,GACzBtB,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,SAEvB,MACCxE,EAAI4F,EAAarI,EAAMmI,EACxB,EAyoCCsB,wBACA8T,eA/1BFvd,GAEAsB,EACEM,EACE0H,EAAOD,MAAQhB,EAAc9D,EAC7BvE,EACA0F,EAAM+C,iBAAmB7G,EAAI2C,EAAgBvE,EAAM,IAAM,KA01B3Dgc,UACAwB,oBAzBwB,IAC1BrQ,EAAWnE,EAAS1E,gBACnB0E,EAAS1E,gBAA6BmZ,MAAMtV,IAC3CwU,GAAMxU,EAAQa,EAAS0U,cACvBrW,EAAUC,MAAMnB,KAAK,CACnBS,WAAW,GACX,IAoBFQ,iBAlCFmR,IAEA/R,EAAa,IACRA,KACA+R,EACJ,EA8BCoF,aAhSkB3X,IAChB7D,EAAU6D,KACZqB,EAAUC,MAAMnB,KAAK,CAAEH,aACvB2G,EACExD,GACA,CAACS,EAAK5J,KACJ,MAAM8M,EAAsBlL,EAAIuH,EAASnJ,GACrC8M,IACFlD,EAAI5D,SAAW8G,EAAa1D,GAAGpD,UAAYA,EAEvCxG,MAAMC,QAAQqN,EAAa1D,GAAG2D,OAChCD,EAAa1D,GAAG2D,KAAKiK,SAAS3H,IAC5BA,EAASrJ,SAAW8G,EAAa1D,GAAGpD,UAAYA,CAAQ,IAG7D,GAEH,GACA,GAEH,EA6QCqB,YACA1C,kBACAiZ,WAvoCgB1W,IAClBV,EAAWU,OAASA,EACpBG,EAAUC,MAAMnB,KAAK,CACnBe,OAAQV,EAAWU,OACnBD,SAAS,GACT,EAmoCA,WAAIkC,GACF,OAAOA,CACR,EACD,eAAId,GACF,OAAOA,CACR,EACD,UAAIiB,GACF,OAAOA,CACR,EACD,UAAIA,CAAOnK,GACTmK,EAASnK,CACV,EACD,kBAAIoF,GACF,OAAOA,CACR,EACD,UAAImD,GACF,OAAOA,CACR,EACD,UAAIA,CAAOvI,GACTuI,EAASvI,CACV,EACD,cAAIqH,GACF,OAAOA,CACR,EACD,cAAIA,CAAWrH,GACbqH,EAAarH,CACd,EACD,YAAI6J,GACF,OAAOA,CACR,EACD,YAAIA,CAAS7J,GACX6J,EAAW,IACNA,KACA7J,EAEN,GAEHka,WACAxQ,YACA4S,gBACA5T,MA9fwC,CACxC7H,EAIA+B,IAEAoL,EAAWnN,GACPqH,EAAUc,OAAOjC,UAAU,CACzBC,KAAO0X,GACL7d,EACEsI,OAAU3G,EAAWI,GACrB8b,KAONvV,EACEtI,EACA+B,GACA,GAyeNyX,WACAV,aACA6D,SACAmB,WArQkD,CAAC9d,EAAM4L,EAAU,CAAA,KAC/DhK,EAAIuH,EAASnJ,KACXyB,EAAYmK,EAAQ7J,cACtByX,EAASxZ,EAAMe,EAAYa,EAAI2C,EAAgBvE,MAE/CwZ,EACExZ,EACA4L,EAAQ7J,cAKVU,EAAI8B,EAAgBvE,EAAMe,EAAY6K,EAAQ7J,gBAG3C6J,EAAQmP,aACXzI,GAAM9L,EAAWM,cAAe9G,GAG7B4L,EAAQkP,YACXxI,GAAM9L,EAAWK,YAAa7G,GAC9BwG,EAAWG,QAAUiF,EAAQ7J,aACzB6V,EAAU5X,EAAMe,EAAYa,EAAI2C,EAAgBvE,KAChD4X,KAGDhM,EAAQiP,YACXvI,GAAM9L,EAAWU,OAAQlH,GACzB2E,EAAgBsC,SAAWM,KAG7BF,EAAUC,MAAMnB,KAAK,IAAKK,IAC3B,EAsODuX,YApiBqD/d,IACrDA,GACEkF,EAAsBlF,GAAMgX,SAASgH,GACnC1L,GAAM9L,EAAWU,OAAQ8W,KAG7B3W,EAAUC,MAAMnB,KAAK,CACnBe,OAAQlH,EAAOwG,EAAWU,OAAS,CAAE,GACrC,EA6hBFsC,cACA+Q,YACA0D,SAxG8C,CAACje,EAAM4L,EAAU,CAAA,KAC/D,MAAM1C,EAAQtH,EAAIuH,EAASnJ,GACrBoV,EAAiBlM,GAASA,EAAME,GAEtC,GAAIgM,EAAgB,CAClB,MAAMiG,EAAWjG,EAAerI,KAC5BqI,EAAerI,KAAK,GACpBqI,EAAexL,IAEfyR,EAASvR,QACXuR,EAASvR,QACT8B,EAAQsS,cAAgB7C,EAAStR,SAEpC,GA4FDuQ,iBAEJ,c/Cj5CE5U,GACGA,EAAMyY,OAAO3V,EAAmC9C,WEpBrD,SAGEA,GACA,MAAMY,EAAUvC,KACTqa,EAASC,GAAcxa,EAAM0C,UAAS,IACvCpC,QACJA,EAAUmC,EAAQnC,QAAOX,SACzBA,EAAQ8a,SACRA,EAAQ/U,OACRA,EAAM0T,OACNA,EAASnS,EAAYyT,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAON,OACPA,EAAMO,UACNA,EAASC,eACTA,KACGC,GACDlZ,EAEEmZ,EAASpQ,MAAO9O,IACpB,IAAImf,GAAW,EACX7f,EAAO,SAELkF,EAAQsX,cAAahN,MAAOzN,IAChC,MAAM+d,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUne,EAC/B,CAAC,MAAMoe,GAAE,CAEV,MAAMC,EAAoB5U,EAAQtG,EAAQkE,aAE1C,IAAK,MAAMhH,KAAOge,EAChBN,EAASO,OAAOje,EAAKge,EAAkBhe,IAazC,GAVImC,SACIA,EAAS,CACbxC,OACArB,QACAsd,SACA8B,WACAE,iBAIA1V,EACF,IACE,MAAMgW,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACAlZ,MAAMnG,GAAUA,GAASA,EAAMyU,SAAS,UAEpC4L,QAAiBC,MAAMlW,EAAQ,CACnC0T,SACAsB,QAAS,IACJA,KACCC,EAAU,CAAE,eAAgBA,GAAY,CAAA,GAE9CkB,KAAMH,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASG,QACzBH,EAASG,OAAS,KAAOH,EAASG,QAAU,MAEhDb,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrBvgB,EAAO2gB,OAAOJ,EAASG,SAEvBjB,GAAaA,EAAU,CAAEc,YAE5B,CAAC,MAAOhV,GACPsU,GAAW,EACXL,GAAWA,EAAQ,CAAEjU,SACtB,CACF,GAxDGrG,CAyDHxE,GAECmf,GAAYpZ,EAAMvB,UACpBuB,EAAMvB,QAAQkD,UAAUC,MAAMnB,KAAK,CACjCkQ,oBAAoB,IAEtB3Q,EAAMvB,QAAQoW,SAAS,cAAe,CACpCtb,SAEH,EAOH,OAJA4E,EAAMiC,WAAU,KACduY,GAAW,EAAK,GACf,IAEIF,EACLta,EAAAgc,cAAAhc,EAAAic,SAAA,KACG3B,EAAO,CACNU,YAIJhb,EAAAgc,cAAA,OAAA,CACEE,WAAY3B,EACZ7U,OAAQA,EACR0T,OAAQA,EACRuB,QAASA,EACThb,SAAUqb,KACND,GAEHN,EAGP,iBdhEE5Y,IAEA,MAAM4Y,SAAEA,KAAatd,GAAS0E,EAC9B,OACE7B,EAAAgc,cAACjc,EAAgBoc,SAAQ,CAAC7gB,MAAO6B,GAC9Bsd,EAEH,qE4DTE,SAMJ5Y,GAEA,MAAMY,EAAUvC,KACVI,QACJA,EAAUmC,EAAQnC,QAAOnE,KACzBA,EAAIigB,QACJA,EAAU,KAAIxX,iBACdA,GACE/C,GACGgE,EAAQwW,GAAarc,EAAM0C,SAASpC,EAAQoZ,eAAevd,IAC5DmgB,EAAMtc,EAAM+B,OAChBzB,EAAQoZ,eAAevd,GAAM+H,IAAImD,IAE7BkV,EAAYvc,EAAM+B,OAAO8D,GACzBvC,EAAQtD,EAAM+B,OAAO5F,GACrBqgB,EAAYxc,EAAM+B,QAAO,GAE/BuB,EAAMtB,QAAU7F,EAChBogB,EAAUva,QAAU6D,EACpBvF,EAAQuD,OAAOiB,MAAMb,IAAI9H,GAEzB0F,EAAMoD,OACH3E,EAAkC0E,SACjC7I,EACA0F,EAAMoD,OAGVrD,EAAa,CACXU,KAAM,EACJgC,SACAnI,KAAMsgB,MAKN,GAAIA,IAAmBnZ,EAAMtB,UAAYya,EAAgB,CACvD,MAAMzN,EAAcjR,EAAIuG,EAAQhB,EAAMtB,SAClCrG,MAAMC,QAAQoT,KAChBqN,EAAUrN,GACVsN,EAAIta,QAAUgN,EAAY9K,IAAImD,GAEjC,GAEHjF,QAAS9B,EAAQkD,UAAUsB,QAG7B,MAAM4X,EAAe1c,EAAM8F,aAMvB6W,IAEAH,EAAUxa,SAAU,EACpB1B,EAAQ6Y,kBAAkBhd,EAAMwgB,EAAwB,GAE1D,CAACrc,EAASnE,IAyQZ,OAlGA6D,EAAMiC,WAAU,KAQd,GAPA3B,EAAQmF,OAAOC,QAAS,EAExBgD,EAAUvM,EAAMmE,EAAQuD,SACtBvD,EAAQkD,UAAUC,MAAMnB,KAAK,IACxBhC,EAAQqC,aAIb6Z,EAAUxa,WACRmG,EAAmB7H,EAAQ6E,SAASiD,MAAMC,YAC1C/H,EAAQqC,WAAWoP,aAErB,GAAIzR,EAAQ6E,SAAS4N,SACnBzS,EAAQ0S,eAAe,CAAC7W,IAAOyd,MAAMzb,IACnC,MAAMwI,EAAQ5I,EAAII,EAAOkF,OAAQlH,GAC3BygB,EAAgB7e,EAAIuC,EAAQqC,WAAWU,OAAQlH,IAGnDygB,GACMjW,GAASiW,EAAcxhB,MACxBuL,IACEiW,EAAcxhB,OAASuL,EAAMvL,MAC5BwhB,EAAcxW,UAAYO,EAAMP,SACpCO,GAASA,EAAMvL,QAEnBuL,EACI/H,EAAI0B,EAAQqC,WAAWU,OAAQlH,EAAMwK,GACrC8H,GAAMnO,EAAQqC,WAAWU,OAAQlH,GACrCmE,EAAQkD,UAAUC,MAAMnB,KAAK,CAC3Be,OAAQ/C,EAAQqC,WAAWU,SAE9B,QAEE,CACL,MAAMgC,EAAetH,EAAIuC,EAAQgF,QAASnJ,IAExCkJ,IACAA,EAAME,IAEJ4C,EAAmB7H,EAAQ6E,SAAS6M,gBAAgB3J,YACpDF,EAAmB7H,EAAQ6E,SAASiD,MAAMC,YAG5CsC,GACEtF,EACA/E,EAAQkE,YACRlE,EAAQ6E,SAAS8L,eAAiBzR,EAAgBK,IAClDS,EAAQ6E,SAAS0F,2BACjB,GACA+O,MACCjT,IACE5F,EAAc4F,IACfrG,EAAQkD,UAAUC,MAAMnB,KAAK,CAC3Be,OAAQ8F,EACN7I,EAAQqC,WAAWU,OACnBsD,EACAxK,MAKX,CAGHmE,EAAQkD,UAAUc,OAAOhC,KAAK,CAC5BnG,OACAmI,OAAQ,IAAKhE,EAAQkE,eAGvBlE,EAAQuD,OAAOoC,OACb6C,EAAsBxI,EAAQgF,SAAS,CAACS,EAAKvI,KAC3C,GACE8C,EAAQuD,OAAOoC,OACfzI,EAAImE,WAAWrB,EAAQuD,OAAOoC,QAC9BF,EAAIE,MAGJ,OADAF,EAAIE,QACG,CAEF,IAGX3F,EAAQuD,OAAOoC,MAAQ,GAEvB3F,EAAQoD,eACR8Y,EAAUxa,SAAU,CAAK,GACxB,CAAC6D,EAAQ1J,EAAMmE,IAElBN,EAAMiC,WAAU,MACblE,EAAIuC,EAAQkE,YAAarI,IAASmE,EAAQ6Y,kBAAkBhd,GAEtD,MACJmE,EAAQ6E,SAASP,kBAAoBA,IACpCtE,EAAQqF,WAAWxJ,EAAgC,IAEtD,CAACA,EAAMmE,EAAS8b,EAASxX,IAErB,CACLiY,KAAM7c,EAAM8F,aAxLD,CAACyI,EAAgBC,KAC5B,MAAMmO,EAA0Brc,EAAQoZ,eAAevd,GACvDmS,GAAYqO,EAAyBpO,EAAQC,GAC7CF,GAAYgO,EAAIta,QAASuM,EAAQC,GACjCkO,EAAaC,GACbN,EAAUM,GACVrc,EAAQ6Y,kBACNhd,EACAwgB,EACArO,GACA,CACEkL,KAAMjL,EACNkL,KAAMjL,IAER,EACD,GAyK6B,CAACkO,EAAcvgB,EAAMmE,IACnDwc,KAAM9c,EAAM8F,aAvKD,CAAC4H,EAAcC,KAC1B,MAAMgP,EAA0Brc,EAAQoZ,eAAevd,GACvDsR,GAAYkP,EAAyBjP,EAAMC,GAC3CF,GAAY6O,EAAIta,QAAS0L,EAAMC,GAC/B+O,EAAaC,GACbN,EAAUM,GACVrc,EAAQ6Y,kBACNhd,EACAwgB,EACAlP,GACA,CACE+L,KAAM9L,EACN+L,KAAM9L,IAER,EACD,GAwJ6B,CAAC+O,EAAcvgB,EAAMmE,IACnDyc,QAAS/c,EAAM8F,aAjPD,CACdxK,EAGAyM,KAEA,MAAMiV,EAAe3b,EAAsBnE,EAAY5B,IACjDqhB,EAA0B9O,GAC9BvN,EAAQoZ,eAAevd,GACvB6gB,GAEF1c,EAAQuD,OAAOoC,MAAQ6B,EAAkB3L,EAAM,EAAG4L,GAClDuU,EAAIta,QAAU6L,GAAUyO,EAAIta,QAASgb,EAAa9Y,IAAImD,IACtDqV,EAAaC,GACbN,EAAUM,GACVrc,EAAQ6Y,kBAAkBhd,EAAMwgB,EAAyB9O,GAAW,CAClE2L,KAAMjM,GAAejS,IACrB,GAgOkC,CAACohB,EAAcvgB,EAAMmE,IACzDmb,OAAQzb,EAAM8F,aA1QD,CACbxK,EAGAyM,KAEA,MAAMkV,EAAc5b,EAAsBnE,EAAY5B,IAChDqhB,EAA0BrP,GAC9BhN,EAAQoZ,eAAevd,GACvB8gB,GAEF3c,EAAQuD,OAAOoC,MAAQ6B,EACrB3L,EACAwgB,EAAwB5d,OAAS,EACjCgJ,GAEFuU,EAAIta,QAAUsL,GAASgP,EAAIta,QAASib,EAAY/Y,IAAImD,IACpDqV,EAAaC,GACbN,EAAUM,GACVrc,EAAQ6Y,kBAAkBhd,EAAMwgB,EAAyBrP,GAAU,CACjEkM,KAAMjM,GAAejS,IACrB,GAqPgC,CAACohB,EAAcvgB,EAAMmE,IACvD4c,OAAQld,EAAM8F,aA/NAjH,IACd,MAAM8d,EAEA7O,GAAcxN,EAAQoZ,eAAevd,GAAO0C,GAClDyd,EAAIta,QAAU8L,GAAcwO,EAAIta,QAASnD,GACzC6d,EAAaC,GACbN,EAAUM,GACVrc,EAAQ6Y,kBAAkBhd,EAAMwgB,EAAyB7O,GAAe,CACtE0L,KAAM3a,GACN,GAsNgC,CAAC6d,EAAcvgB,EAAMmE,IACvDkN,OAAQxN,EAAM8F,aApND,CACbjH,EACAvD,EAGAyM,KAEA,MAAMoV,EAAc9b,EAAsBnE,EAAY5B,IAChDqhB,EAA0BS,GAC9B9c,EAAQoZ,eAAevd,GACvB0C,EACAse,GAEF7c,EAAQuD,OAAOoC,MAAQ6B,EAAkB3L,EAAM0C,EAAOkJ,GACtDuU,EAAIta,QAAUob,GAASd,EAAIta,QAASnD,EAAOse,EAAYjZ,IAAImD,IAC3DqV,EAAaC,GACbN,EAAUM,GACVrc,EAAQ6Y,kBAAkBhd,EAAMwgB,EAAyBS,GAAU,CACjE5D,KAAM3a,EACN4a,KAAMlM,GAAejS,IACrB,GAgMgC,CAACohB,EAAcvgB,EAAMmE,IACvD+c,OAAQrd,EAAM8F,aA1JD,CACbjH,EACAvD,KAEA,MAAMiJ,EAAcrH,EAAY5B,GAC1BqhB,EAA0B5N,GAC9BzO,EAAQoZ,eAENvd,GACF0C,EACA0F,GAEF+X,EAAIta,QAAU,IAAI2a,GAAyBzY,KAAI,CAACoZ,EAAMtP,IACnDsP,GAAQtP,IAAMnP,EAAuByd,EAAIta,QAAQgM,GAA3B3G,MAEzBqV,EAAaC,GACbN,EAAU,IAAIM,IACdrc,EAAQ6Y,kBACNhd,EACAwgB,EACA5N,GACA,CACEyK,KAAM3a,EACN4a,KAAMlV,IAER,GACA,EACD,GA+HiC,CAACmY,EAAcvgB,EAAMmE,IACvD3B,QAASqB,EAAM8F,aA5HfxK,IAIA,MAAMqhB,EAA0Btb,EAAsBnE,EAAY5B,IAClEghB,EAAIta,QAAU2a,EAAwBzY,IAAImD,GAC1CqV,EAAa,IAAIC,IACjBN,EAAU,IAAIM,IACdrc,EAAQ6Y,kBACNhd,EACA,IAAIwgB,IACAxf,GAAeA,GACnB,CAAA,GACA,GACA,EACD,GA6GmC,CAACuf,EAAcvgB,EAAMmE,IACzDuF,OAAQ7F,EAAMud,SACZ,IACE1X,EAAO3B,KAAI,CAACmB,EAAOxG,KAAW,IACzBwG,EACH+W,CAACA,GAAUE,EAAIta,QAAQnD,IAAUwI,SAErC,CAACxB,EAAQuW,IAGf,YC7XgB,SAKdva,EAA8C,IAE9C,MAAM2b,EAAexd,EAAM+B,SAGrB0b,EAAUzd,EAAM+B,UACf1B,EAAWc,GAAmBnB,EAAM0C,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWuG,EAAWzH,EAAMpB,eAC5BsR,aAAa,EACbQ,cAAc,EACdC,oBAAoB,EACpBpP,SAAS,EACTkP,YAAa,EACbtP,YAAa,CAAE,EACfC,cAAe,CAAE,EACjBC,iBAAkB,CAAE,EACpBG,OAAQxB,EAAMwB,QAAU,CAAE,EAC1BlB,SAAUN,EAAMM,WAAY,EAC5B1B,cAAe6I,EAAWzH,EAAMpB,oBAC5B3C,EACA+D,EAAMpB,gBAGP+c,EAAaxb,UAChBwb,EAAaxb,QAAU,IAClBoQ,GAAkBvQ,GACrBxB,cAIJ,MAAMC,EAAUkd,EAAaxb,QAAQ1B,QAkFrC,OAjFAA,EAAQ6E,SAAWtD,EAEnBD,EAAa,CACXQ,QAAS9B,EAAQkD,UAAUC,MAC3BnB,KACEhH,IAGE2F,EACE3F,EACAgF,EAAQQ,gBACRR,EAAQiD,kBACR,IAGFpC,EAAgB,IAAKb,EAAQqC,YAC9B,IAIL3C,EAAMiC,WACJ,IAAM3B,EAAQwZ,aAAajY,EAAMM,WACjC,CAAC7B,EAASuB,EAAMM,WAGlBnC,EAAMiC,WAAU,KACd,GAAI3B,EAAQQ,gBAAgBgC,QAAS,CACnC,MAAMA,EAAUxC,EAAQyT,YACpBjR,IAAYzC,EAAUyC,SACxBxC,EAAQkD,UAAUC,MAAMnB,KAAK,CAC3BQ,WAGL,IACA,CAACxC,EAASD,EAAUyC,UAEvB9C,EAAMiC,WAAU,KACVJ,EAAMyC,SAAWkL,GAAU3N,EAAMyC,OAAQmZ,EAAQzb,UACnD1B,EAAQ6X,OAAOtW,EAAMyC,OAAQhE,EAAQ6E,SAAS0U,cAC9C4D,EAAQzb,QAAUH,EAAMyC,OACxBnD,GAAiBsC,IAAK,IAAWA,OAEjCnD,EAAQqZ,qBACT,GACA,CAAC9X,EAAMyC,OAAQhE,IAElBN,EAAMiC,WAAU,KACVJ,EAAMwB,QACR/C,EAAQyZ,WAAWlY,EAAMwB,OAC1B,GACA,CAACxB,EAAMwB,OAAQ/C,IAElBN,EAAMiC,WAAU,KACT3B,EAAQmF,OAAOD,QAClBlF,EAAQoD,eACRpD,EAAQmF,OAAOD,OAAQ,GAGrBlF,EAAQmF,OAAOzB,QACjB1D,EAAQmF,OAAOzB,OAAQ,EACvB1D,EAAQkD,UAAUC,MAAMnB,KAAK,IAAKhC,EAAQqC,cAG5CrC,EAAQoE,kBAAkB,IAG5B1E,EAAMiC,WAAU,KACdJ,EAAM+C,kBACJtE,EAAQkD,UAAUc,OAAOhC,KAAK,CAC5BgC,OAAQhE,EAAQmE,aAChB,GACH,CAAC5C,EAAM+C,iBAAkBtE,IAE5BN,EAAMiC,WAAU,KACVub,EAAaxb,UACfwb,EAAaxb,QAAQgC,MAAQwZ,EAAaxb,QAAQgC,MAAM8H,KAAK,CAAA,GAC9D,GACA,CAACzL,IAEJmd,EAAaxb,QAAQ3B,UAAYD,EAAkBC,EAAWC,GAEvDkd,EAAaxb,OACtB"}