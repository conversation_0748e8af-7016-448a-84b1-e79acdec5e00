import { useState, useEffect } from 'react';
import { But<PERSON> } from './button';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { Textarea } from './textarea';
import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { 
  Star, 
  ThumbsUp, 
  ThumbsDown, 
  MessageCircle,
  Send,
  Flag
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

interface Review {
  id: string;
  rating: number;
  comment: string;
  created_at: string;
  buyer: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
  };
  helpful_count: number;
  is_verified_purchase: boolean;
}

interface ReviewSystemProps {
  productId: string;
  averageRating: number;
  totalReviews: number;
  canReview?: boolean;
  className?: string;
}

export const ReviewSystem = ({ 
  productId, 
  averageRating, 
  totalReviews, 
  canReview = false,
  className = "" 
}: ReviewSystemProps) => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 5,
    comment: ''
  });
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadReviews();
  }, [productId]);

  const loadReviews = async () => {
    setLoading(true);
    try {
      // In a real implementation, you would fetch from reviews table
      // For now, we'll use mock data
      const mockReviews: Review[] = [
        {
          id: '1',
          rating: 5,
          comment: 'منتج ممتاز! استفدت منه كثيراً في مشروعي. أنصح به بشدة.',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
          buyer: {
            id: '1',
            username: 'ahmed_dev',
            full_name: 'أحمد محمد',
          },
          helpful_count: 12,
          is_verified_purchase: true,
        },
        {
          id: '2',
          rating: 4,
          comment: 'جودة عالية ومحتوى مفيد. كان هناك بعض النقاط التي كنت أتمنى أن تكون أكثر تفصيلاً.',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
          buyer: {
            id: '2',
            username: 'sara_designer',
            full_name: 'سارة أحمد',
          },
          helpful_count: 8,
          is_verified_purchase: true,
        },
      ];

      setReviews(mockReviews);
    } catch (error) {
      console.error('Error loading reviews:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحميل المراجعات",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const submitReview = async () => {
    if (!profile) {
      toast({
        title: "خطأ",
        description: "يجب تسجيل الدخول لإضافة مراجعة",
        variant: "destructive",
      });
      return;
    }

    if (!newReview.comment.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى كتابة تعليق",
        variant: "destructive",
      });
      return;
    }

    setSubmitting(true);
    try {
      const { error } = await supabase
        .from('reviews')
        .insert({
          product_id: productId,
          buyer_id: profile.id,
          rating: newReview.rating,
          comment: newReview.comment,
        });

      if (error) throw error;

      toast({
        title: "شكراً لك!",
        description: "تم إضافة مراجعتك بنجاح",
      });

      setShowReviewForm(false);
      setNewReview({ rating: 5, comment: '' });
      loadReviews();
    } catch (error: any) {
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إضافة المراجعة",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    };

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatTime = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { 
      addSuffix: true, 
      locale: ar 
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Rating Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-400" />
            التقييمات والمراجعات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="text-center">
              <div className="text-3xl font-bold">{averageRating.toFixed(1)}</div>
              {renderStars(averageRating, 'lg')}
              <div className="text-sm text-gray-600 mt-1">
                {totalReviews} مراجعة
              </div>
            </div>
            <div className="flex-1">
              {/* Rating Distribution */}
              {[5, 4, 3, 2, 1].map((rating) => {
                const count = reviews.filter(r => r.rating === rating).length;
                const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
                
                return (
                  <div key={rating} className="flex items-center gap-2 mb-1">
                    <span className="text-sm w-8">{rating}</span>
                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-8">{count}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {canReview && (
            <Button 
              onClick={() => setShowReviewForm(true)}
              className="w-full"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              اكتب مراجعة
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Review Form */}
      {showReviewForm && (
        <Card>
          <CardHeader>
            <CardTitle>إضافة مراجعة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">التقييم</label>
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setNewReview({ ...newReview, rating: star })}
                    className="p-1"
                  >
                    <Star
                      className={`h-6 w-6 ${
                        star <= newReview.rating 
                          ? 'text-yellow-400 fill-current' 
                          : 'text-gray-300 hover:text-yellow-200'
                      }`}
                    />
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">التعليق</label>
              <Textarea
                placeholder="شاركنا رأيك في هذا المنتج..."
                value={newReview.comment}
                onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}
                rows={4}
              />
            </div>

            <div className="flex gap-2">
              <Button 
                onClick={submitReview} 
                disabled={submitting}
                className="flex-1"
              >
                {submitting ? (
                  <>جاري الإرسال...</>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    إرسال المراجعة
                  </>
                )}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowReviewForm(false)}
              >
                إلغاء
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            جاري تحميل المراجعات...
          </div>
        ) : reviews.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">لا توجد مراجعات بعد</h3>
              <p className="text-gray-600">كن أول من يراجع هذا المنتج</p>
            </CardContent>
          </Card>
        ) : (
          reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={review.buyer.avatar_url} />
                    <AvatarFallback>
                      {review.buyer.full_name?.charAt(0) || review.buyer.username.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">{review.buyer.full_name || review.buyer.username}</span>
                      {review.is_verified_purchase && (
                        <Badge variant="secondary" className="text-xs">
                          مشترٍ موثق
                        </Badge>
                      )}
                      <span className="text-sm text-gray-500">
                        {formatTime(review.created_at)}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2 mb-2">
                      {renderStars(review.rating)}
                    </div>
                    
                    <p className="text-gray-700 mb-3">{review.comment}</p>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <button className="flex items-center gap-1 hover:text-green-600">
                        <ThumbsUp className="h-3 w-3" />
                        مفيد ({review.helpful_count})
                      </button>
                      <button className="flex items-center gap-1 hover:text-red-600">
                        <Flag className="h-3 w-3" />
                        إبلاغ
                      </button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
