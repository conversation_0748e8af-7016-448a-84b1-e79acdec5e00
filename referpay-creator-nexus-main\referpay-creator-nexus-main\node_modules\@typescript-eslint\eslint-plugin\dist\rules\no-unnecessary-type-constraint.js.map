{"version": 3, "file": "no-unnecessary-type-constraint.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-constraint.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,yCAAoC;AACpC,+CAAiC;AAIjC,kCAAqC;AAOrC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,gCAAgC;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,mDAAmD;YAChE,WAAW,EAAE,aAAa;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,2BAA2B,EACzB,qDAAqD;YACvD,qBAAqB,EACnB,+FAA+F;SAClG;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,iFAAiF;QACjF,sFAAsF;QACtF,yFAAyF;QACzF,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;YACrC,CAAC,sBAAc,CAAC,YAAY,EAAE,KAAK,CAAC;YACpC,CAAC,sBAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC;SAC7C,CAAC,CAAC;QAEH,SAAS,6CAA6C,CACpD,QAAgB;YAEhB,MAAM,OAAO,GAAG,IAAA,mBAAO,EAAC,QAAQ,CAAC,CAAC,iBAAiB,EAAkB,CAAC;YACtE,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;gBACtB,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;gBACtB,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG;oBACnB,OAAO,IAAI,CAAC;gBAEd;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,MAAM,wCAAwC,GAC5C,6CAA6C,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAElE,MAAM,SAAS,GAAG,CAChB,IAAiC,EACjC,eAAwB,EAClB,EAAE;YACR,MAAM,UAAU,GAAG,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACpE,SAAS,sBAAsB;gBAC7B,IAAI,CAAC,eAAe,IAAI,CAAC,wCAAwC,EAAE,CAAC;oBAClE,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,6CAA6C;gBAC7C,OAAO,CACJ,IAAI,CAAC,MAA8C,CAAC,MAAM,CAAC,MAAM;oBAChE,CAAC;oBACH,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG;oBACxD,CAAC,IAAI,CAAC,OAAO,CACd,CAAC;YACJ,CAAC;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;wBACpB,UAAU;qBACX;oBACD,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,6BAA6B;4BACxC,IAAI,EAAE;gCACJ,UAAU;6BACX;4BACD,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9C,sBAAsB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CACpC,CAAC;4BACJ,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;QAEF,OAAO;YACL,0FAA0F,CACxF,IAAiC;gBAEjC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC;YACD,oFAAoF,CAClF,IAAiC;gBAEjC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACxB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}