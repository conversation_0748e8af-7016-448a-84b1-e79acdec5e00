
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { Upload, Package, DollarSign, Tag, Loader2 } from 'lucide-react';
import { ProductCategory } from '@/types/database';

const Create = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { profile, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    category: '' as ProductCategory | '',
    tags: '',
    image: null as File | null,
    file: null as File | null
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, type: 'image' | 'file') => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, [type]: file }));
    }
  };

  const uploadFile = async (file: File, bucket: string): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `${profile?.id}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, file);

      if (uploadError) {
        console.error('Upload error:', uploadError);
        return null;
      }

      const { data } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('File upload error:', error);
      return null;
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!isAuthenticated || !profile) {
      toast({
        title: "خطأ",
        description: "يجب تسجيل الدخول أولاً",
        variant: "destructive",
      });
      return;
    }

    if (!formData.title || !formData.description || !formData.price || !formData.category) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      let coverImageUrl = null;
      let fileUrl = null;

      // Upload cover image if provided
      if (formData.image) {
        coverImageUrl = await uploadFile(formData.image, 'product-images');
        if (!coverImageUrl) {
          throw new Error('فشل في رفع صورة المنتج');
        }
      }

      // Upload product file if provided
      if (formData.file) {
        fileUrl = await uploadFile(formData.file, 'product-files');
        if (!fileUrl) {
          throw new Error('فشل في رفع ملف المنتج');
        }
      }

      // Create product in database
      const { data, error } = await supabase
        .from('products')
        .insert({
          creator_id: profile.id,
          title: formData.title,
          description: formData.description,
          price: parseFloat(formData.price),
          category: formData.category as ProductCategory,
          cover_image_url: coverImageUrl,
          file_url: fileUrl,
          tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : [],
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      toast({
        title: "نجح!",
        description: "تم إنشاء المنتج بنجاح",
      });

      navigate('/dashboard');
    } catch (error: any) {
      console.error('Error creating product:', error);
      toast({
        title: "خطأ",
        description: error.message || "حدث خطأ أثناء إنشاء المنتج",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">يرجى تسجيل الدخول</h2>
              <p className="text-gray-600">تحتاج إلى تسجيل الدخول لإنشاء منتج جديد</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const categories: { value: ProductCategory; label: string }[] = [
    { value: 'ebook', label: 'كتاب إلكتروني' },
    { value: 'design', label: 'تصميم' },
    { value: 'software', label: 'برمجيات' },
    { value: 'course', label: 'دورة تدريبية' },
    { value: 'nft', label: 'NFT' },
    { value: 'smart_contract', label: 'عقد ذكي' },
    { value: 'template', label: 'قالب' },
    { value: 'other', label: 'أخرى' },
  ];

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Header />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">إنشاء منتج جديد</h1>
            <p className="text-gray-600">أنشئ منتجك الرقمي وابدأ في الربح من الإحالات</p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                تفاصيل المنتج
              </CardTitle>
              <CardDescription>
                أدخل المعلومات الأساسية لمنتجك الرقمي
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المنتج</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="أدخل عنوان منتجك"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">وصف المنتج</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="اكتب وصفاً تفصيلياً لمنتجك"
                    rows={4}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price" className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      السعر (بالدولار)
                    </Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', e.target.value)}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">الفئة</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر فئة المنتج" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags" className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    العلامات (اختيارية)
                  </Label>
                  <Input
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    placeholder="فصل بينها بفواصل: تقنية، تصميم، تسويق"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image" className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    صورة المنتج
                  </Label>
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e, 'image')}
                    className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {formData.image && (
                    <p className="text-sm text-gray-600">
                      تم اختيار: {formData.image.name}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file" className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    ملف المنتج
                  </Label>
                  <Input
                    id="file"
                    type="file"
                    onChange={(e) => handleFileUpload(e, 'file')}
                    className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                  />
                  {formData.file && (
                    <p className="text-sm text-gray-600">
                      تم اختيار: {formData.file.name}
                    </p>
                  )}
                </div>

                <div className="flex gap-4 pt-4">
                  <Button type="submit" className="flex-1" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        جاري الإنشاء...
                      </>
                    ) : (
                      'إنشاء المنتج'
                    )}
                  </Button>
                  <Button type="button" variant="outline" className="flex-1" disabled={loading}>
                    حفظ كمسودة
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Create;
