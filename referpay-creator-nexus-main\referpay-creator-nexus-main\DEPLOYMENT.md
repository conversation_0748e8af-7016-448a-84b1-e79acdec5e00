# دليل النشر - Referpay

## متطلبات النشر

### 1. إعداد Supabase

#### إنشاء مشروع جديد
1. انتقل إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. أنشئ مشروع جديد
3. احف<PERSON> URL المشروع و API Key

#### تطبيق قاعدة البيانات
```bash
# تطبيق الجداول الأساسية
psql -h your-project.supabase.co -U postgres -d postgres -f supabase/migrations/20250610192114-77eebcce-06f5-43e5-95fd-a6094774d256.sql

# تطبيق RLS policies
psql -h your-project.supabase.co -U postgres -d postgres -f supabase/rls-policies.sql

# تطبيق Functions
psql -h your-project.supabase.co -U postgres -d postgres -f supabase/functions.sql

# إعداد Storage
psql -h your-project.supabase.co -U postgres -d postgres -f supabase/storage.sql
```

#### إعداد Storage Buckets
1. انتقل إلى Storage في Supabase Dashboard
2. أنشئ البuckets التالية:
   - `product-images` (public)
   - `product-files` (private)
   - `avatars` (public)

### 2. إعداد متغيرات البيئة

#### للتطوير المحلي
```bash
cp .env.example .env
```

املأ المتغيرات في `.env`:
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_NOWPAYMENTS_API_KEY=your-nowpayments-key
VITE_BINANCE_PAY_API_KEY=your-binance-pay-key
VITE_TON_PAY_API_KEY=your-ton-pay-key
VITE_APP_URL=http://localhost:8080
VITE_APP_NAME=Referpay
```

#### للإنتاج
قم بإعداد المتغيرات في منصة النشر (Vercel, Netlify, إلخ)

### 3. النشر على Vercel

#### الطريقة الأولى: عبر GitHub
1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Vercel
3. أضف متغيرات البيئة في إعدادات Vercel
4. انشر المشروع

#### الطريقة الثانية: عبر Vercel CLI
```bash
# تثبيت Vercel CLI
npm i -g vercel

# تسجيل الدخول
vercel login

# النشر
vercel --prod
```

### 4. النشر على Netlify

```bash
# بناء المشروع
npm run build

# تثبيت Netlify CLI
npm i -g netlify-cli

# تسجيل الدخول
netlify login

# النشر
netlify deploy --prod --dir=dist
```

### 5. إعداد أنظمة الدفع

#### NOWPayments
1. سجل في [NOWPayments](https://nowpayments.io/)
2. احصل على API Key
3. أضف الـ API Key لمتغيرات البيئة

#### Binance Pay
1. سجل في [Binance Pay](https://pay.binance.com/)
2. احصل على API credentials
3. أضف المفاتيح لمتغيرات البيئة

#### TON Pay
1. سجل في [TON Pay](https://wallet.ton.org/)
2. احصل على API Key
3. أضف الـ API Key لمتغيرات البيئة

### 6. إعداد النطاق المخصص

#### Vercel
1. انتقل إلى إعدادات المشروع في Vercel
2. أضف النطاق المخصص
3. اتبع تعليمات DNS

#### Netlify
1. انتقل إلى إعدادات الموقع في Netlify
2. أضف النطاق المخصص
3. اتبع تعليمات DNS

### 7. إعداد SSL

معظم منصات النشر توفر SSL تلقائياً. تأكد من:
- تفعيل HTTPS
- إعادة توجيه HTTP إلى HTTPS
- تحديث متغيرات البيئة لاستخدام HTTPS

### 8. مراقبة الأداء

#### إعداد Analytics
```javascript
// Google Analytics
// أضف في index.html
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
```

#### إعداد Error Tracking
```bash
# Sentry
npm install @sentry/react @sentry/tracing
```

### 9. النسخ الاحتياطي

#### قاعدة البيانات
```bash
# نسخ احتياطي يومي
pg_dump -h your-project.supabase.co -U postgres -d postgres > backup-$(date +%Y%m%d).sql
```

#### الملفات
- استخدم Supabase Storage للملفات
- فعل النسخ الاحتياطي التلقائي

### 10. اختبار ما بعد النشر

#### قائمة التحقق
- [ ] تسجيل الدخول والخروج
- [ ] إنشاء منتج جديد
- [ ] رفع الملفات
- [ ] عملية شراء تجريبية
- [ ] نظام الإحالة
- [ ] التقييمات والمراجعات
- [ ] البحث والفلترة
- [ ] الإشعارات
- [ ] الاستجابة للأجهزة المحمولة

#### أدوات الاختبار
```bash
# اختبار الأداء
npm run test

# اختبار التغطية
npm run test:coverage

# اختبار البناء
npm run build
```

### 11. الصيانة

#### التحديثات الدورية
```bash
# تحديث التبعيات
npm update

# فحص الثغرات الأمنية
npm audit

# إصلاح الثغرات
npm audit fix
```

#### مراقبة الأداء
- استخدم Vercel Analytics أو Netlify Analytics
- راقب أوقات التحميل
- تتبع معدلات التحويل

### 12. استكشاف الأخطاء

#### مشاكل شائعة
1. **خطأ في متغيرات البيئة**: تأكد من إعداد جميع المتغيرات
2. **مشاكل CORS**: تحقق من إعدادات Supabase
3. **مشاكل الدفع**: تأكد من صحة API Keys
4. **مشاكل الملفات**: تحقق من إعدادات Storage

#### سجلات الأخطاء
```bash
# عرض سجلات Vercel
vercel logs

# عرض سجلات Netlify
netlify logs
```

### 13. الأمان

#### قائمة التحقق الأمني
- [ ] تفعيل HTTPS
- [ ] إخفاء API Keys الحساسة
- [ ] تفعيل RLS في Supabase
- [ ] تحديث التبعيات بانتظام
- [ ] مراجعة صلاحيات المستخدمين

#### حماية إضافية
```javascript
// إعداد CSP Headers
// في vercel.json أو netlify.toml
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' 'unsafe-inline'"
        }
      ]
    }
  ]
}
```

### 14. التحسين للإنتاج

#### تحسين الأداء
```bash
# تحليل حجم Bundle
npm run build -- --analyze

# ضغط الصور
npm install imagemin imagemin-webp
```

#### تحسين SEO
- إضافة meta tags
- إعداد sitemap.xml
- تحسين أوقات التحميل
- إضافة structured data

---

## الدعم

للحصول على المساعدة:
1. راجع الوثائق
2. تحقق من Issues في GitHub
3. اتصل بفريق الدعم

**نصيحة**: احتفظ بنسخة من هذا الدليل محدثة مع أي تغييرات في عملية النشر.
