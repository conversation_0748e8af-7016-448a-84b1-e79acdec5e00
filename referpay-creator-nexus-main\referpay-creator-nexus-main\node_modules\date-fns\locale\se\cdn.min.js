var q=function(H){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(H)},W=function(H,G){var I=Object.keys(H);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(H);G&&(Y=Y.filter(function(N){return Object.getOwnPropertyDescriptor(H,N).enumerable})),I.push.apply(I,Y)}return I},K=function(H){for(var G=1;G<arguments.length;G++){var I=arguments[G]!=null?arguments[G]:{};G%2?W(Object(I),!0).forEach(function(Y){t(H,Y,I[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(I)):W(Object(I)).forEach(function(Y){Object.defineProperty(H,Y,Object.getOwnPropertyDescriptor(I,Y))})}return H},t=function(H,G,I){if(G=B1(G),G in H)Object.defineProperty(H,G,{value:I,enumerable:!0,configurable:!0,writable:!0});else H[G]=I;return H},B1=function(H){var G=X1(H,"string");return q(G)=="symbol"?G:String(G)},X1=function(H,G){if(q(H)!="object"||!H)return H;var I=H[Symbol.toPrimitive];if(I!==void 0){var Y=I.call(H,G||"default");if(q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,I=function B(C,X){for(var E in X)G(C,E,{get:X[E],enumerable:!0,configurable:!0,set:function J(U){return X[E]=function(){return U}}})},Y={lessThanXSeconds:{one:"unnit go ovtta sekundda",other:"unnit go {{count}} sekundda"},xSeconds:{one:"sekundda",other:"{{count}} sekundda"},halfAMinute:"bealle minuhta",lessThanXMinutes:{one:"unnit go bealle minuhta",other:"unnit go {{count}} minuhta"},xMinutes:{one:"minuhta",other:"{{count}} minuhta"},aboutXHours:{one:"sullii ovtta diimmu",other:"sullii {{count}} diimmu"},xHours:{one:"diimmu",other:"{{count}} diimmu"},xDays:{one:"beaivvi",other:"{{count}} beaivvi"},aboutXWeeks:{one:"sullii ovtta vahku",other:"sullii {{count}} vahku"},xWeeks:{one:"vahku",other:"{{count}} vahku"},aboutXMonths:{one:"sullii ovtta m\xE1nu",other:"sullii {{count}} m\xE1nu"},xMonths:{one:"m\xE1nu",other:"{{count}} m\xE1nu"},aboutXYears:{one:"sullii ovtta jagi",other:"sullii {{count}} jagi"},xYears:{one:"jagi",other:"{{count}} jagi"},overXYears:{one:"guhkit go jagi",other:"guhkit go {{count}} jagi"},almostXYears:{one:"measta jagi",other:"measta {{count}} jagi"}},N=function B(C,X,E){var J,U=Y[C];if(typeof U==="string")J=U;else if(X===1)J=U.one;else J=U.other.replace("{{count}}",String(X));if(E!==null&&E!==void 0&&E.addSuffix)if(E.comparison&&E.comparison>0)return"geah\u010Den "+J;else return J+" \xE1igi";return J};function S(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=C.width?String(C.width):B.defaultWidth,E=B.formats[X]||B.formats[B.defaultWidth];return E}}var z={full:"EEEE MMMM d. 'b.' y",long:"MMMM d. 'b.' y",medium:"MMM d. 'b.' y",short:"dd.MM.y"},D={full:"'dii.' HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},$={full:"{{date}} 'dii.' {{time}}",long:"{{date}} 'dii.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:S({formats:z,defaultWidth:"full"}),time:S({formats:D,defaultWidth:"full"}),dateTime:S({formats:$,defaultWidth:"full"})},R={lastWeek:"'ovddit' eeee 'dii.' p",yesterday:"'ikte dii.' p",today:"'odne dii.' p",tomorrow:"'ihtin dii.' p",nextWeek:"EEEE 'dii.' p",other:"P"},V=function B(C,X,E,J){return R[C]};function x(B){return function(C,X){var E=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",J;if(E==="formatting"&&B.formattingValues){var U=B.defaultFormattingWidth||B.defaultWidth,Z=X!==null&&X!==void 0&&X.width?String(X.width):U;J=B.formattingValues[Z]||B.formattingValues[U]}else{var Q=B.defaultWidth,A=X!==null&&X!==void 0&&X.width?String(X.width):B.defaultWidth;J=B.values[A]||B.values[Q]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var j={narrow:["o.Kr.","m.Kr."],abbreviated:["o.Kr.","m.Kr."],wide:["ovdal Kristusa","ma\u014B\u014Bel Kristusa"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvart\xE1la","2. kvart\xE1la","3. kvart\xE1la","4. kvart\xE1la"]},w={narrow:["O","G","N","C","M","G","S","B","\u010C","G","S","J"],abbreviated:["o\u0111\u0111a","guov","njuk","cuo","mies","geas","suoi","borg","\u010Dak\u010D","golg","sk\xE1b","juov"],wide:["o\u0111\u0111ajagem\xE1nnu","guovvam\xE1nnu","njuk\u010Dam\xE1nnu","cuo\u014Bom\xE1nnu","miessem\xE1nnu","geassem\xE1nnu","suoidnem\xE1nnu","borgem\xE1nnu","\u010Dak\u010Dam\xE1nnu","golggotm\xE1nnu","sk\xE1bmam\xE1nnu","juovlam\xE1nnu"]},v={narrow:["S","V","M","G","D","B","L"],short:["sotn","vuos","ma\u014B","gask","duor","bear","l\xE1v"],abbreviated:["sotn","vuos","ma\u014B","gask","duor","bear","l\xE1v"],wide:["sotnabeaivi","vuoss\xE1rga","ma\u014B\u014Beb\xE1rga","gaskavahkku","duorastat","bearjadat","l\xE1vvardat"]},P={narrow:{am:"a",pm:"p",midnight:"gaskaidja",noon:"gaskabeaivi",morning:"i\u0111\u0111es",afternoon:"ma\u014B\u014Bel gaska.",evening:"eahkes",night:"ihkku"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"gaskaidja",noon:"gaskabeaivvi",morning:"i\u0111\u0111es",afternoon:"ma\u014B\u014Bel gaskabea.",evening:"eahkes",night:"ihkku"},wide:{am:"a.m.",pm:"p.m.",midnight:"gaskaidja",noon:"gaskabeavvi",morning:"i\u0111\u0111es",afternoon:"ma\u014B\u014Bel gaskabeaivvi",evening:"eahkes",night:"ihkku"}},_=function B(C,X){var E=Number(C);return E+"."},F={ordinalNumber:_,era:x({values:j,defaultWidth:"wide"}),quarter:x({values:f,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:x({values:w,defaultWidth:"wide"}),day:x({values:v,defaultWidth:"wide"}),dayPeriod:x({values:P,defaultWidth:"wide"})};function O(B){return function(C){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=X.width,J=E&&B.matchPatterns[E]||B.matchPatterns[B.defaultMatchWidth],U=C.match(J);if(!U)return null;var Z=U[0],Q=E&&B.parsePatterns[E]||B.parsePatterns[B.defaultParseWidth],A=Array.isArray(Q)?b(Q,function(M){return M.test(Z)}):m(Q,function(M){return M.test(Z)}),T;T=B.valueCallback?B.valueCallback(A):A,T=X.valueCallback?X.valueCallback(T):T;var a=C.slice(Z.length);return{value:T,rest:a}}}var m=function B(C,X){for(var E in C)if(Object.prototype.hasOwnProperty.call(C,E)&&X(C[E]))return E;return},b=function B(C,X){for(var E=0;E<C.length;E++)if(X(C[E]))return E;return};function k(B){return function(C){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},E=C.match(B.matchPattern);if(!E)return null;var J=E[0],U=C.match(B.parsePattern);if(!U)return null;var Z=B.valueCallback?B.valueCallback(U[0]):U[0];Z=X.valueCallback?X.valueCallback(Z):Z;var Q=C.slice(J.length);return{value:Z,rest:Q}}}var h=/^(\d+)\.?/i,c=/\d+/i,y={narrow:/^(o\.? ?Kr\.?|m\.? ?Kr\.?)/i,abbreviated:/^(o\.? ?Kr\.?|m\.? ?Kr\.?)/i,wide:/^(ovdal Kristusa|ovdal min áiggi|maŋŋel Kristusa|min áigi)/i},u={any:[/^o/i,/^m/i]},p={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? kvartála/i},g={any:[/1/i,/2/i,/3/i,/4/i]},d={narrow:/^[ogncmsbčj]/i,abbreviated:/^(ođđa|guov|njuk|cuo|mies|geas|suoi|borg|čakč|golg|skáb|juov)\.?/i,wide:/^(ođđajagemánnu|guovvamánnu|njukčamánnu|cuoŋománnu|miessemánnu|geassemánnu|suoidnemánnu|borgemánnu|čakčamánnu|golggotmánnu|skábmamánnu|juovlamánnu)/i},l={narrow:[/^o/i,/^g/i,/^n/i,/^c/i,/^m/i,/^g/i,/^s/i,/^b/i,/^č/i,/^g/i,/^s/i,/^j/i],any:[/^o/i,/^gu/i,/^n/i,/^c/i,/^m/i,/^ge/i,/^su/i,/^b/i,/^č/i,/^go/i,/^sk/i,/^j/i]},i={narrow:/^[svmgdbl]/i,short:/^(sotn|vuos|maŋ|gask|duor|bear|láv)/i,abbreviated:/^(sotn|vuos|maŋ|gask|duor|bear|láv)/i,wide:/^(sotnabeaivi|vuossárga|maŋŋebárga|gaskavahkku|duorastat|bearjadat|lávvardat)/i},n={any:[/^s/i,/^v/i,/^m/i,/^g/i,/^d/i,/^b/i,/^l/i]},s={narrow:/^(gaskaidja|gaskabeaivvi|(på) (iđđes|maŋŋel gaskabeaivvi|eahkes|ihkku)|[ap])/i,any:/^([ap]\.?\s?m\.?|gaskaidja|gaskabeaivvi|(på) (iđđes|maŋŋel gaskabeaivvi|eahkes|ihkku))/i},o={any:{am:/^a(\.?\s?m\.?)?$/i,pm:/^p(\.?\s?m\.?)?$/i,midnight:/^gaskai/i,noon:/^gaskab/i,morning:/iđđes/i,afternoon:/maŋŋel gaskabeaivvi/i,evening:/eahkes/i,night:/ihkku/i}},r={ordinalNumber:k({matchPattern:h,parsePattern:c,valueCallback:function B(C){return parseInt(C,10)}}),era:O({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:s,defaultMatchWidth:"any",parsePatterns:o,defaultParseWidth:"any"})},e={code:"se",formatDistance:N,formatLong:L,formatRelative:V,localize:F,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{se:e})})})();

//# debugId=397CDE6B0233F8A164756e2164756e21
