var q=function(B){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(U){return typeof U}:function(U){return U&&typeof Symbol=="function"&&U.constructor===Symbol&&U!==Symbol.prototype?"symbol":typeof U},q(B)},W=function(B,U){var X=Object.keys(B);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(B);U&&(Y=Y.filter(function(M){return Object.getOwnPropertyDescriptor(B,M).enumerable})),X.push.apply(X,Y)}return X},K=function(B){for(var U=1;U<arguments.length;U++){var X=arguments[U]!=null?arguments[U]:{};U%2?W(Object(X),!0).forEach(function(Y){EE(B,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(X)):W(Object(X)).forEach(function(Y){Object.defineProperty(B,Y,Object.getOwnPropertyDescriptor(X,Y))})}return B},EE=function(B,U,X){if(U=xE(U),U in B)Object.defineProperty(B,U,{value:X,enumerable:!0,configurable:!0,writable:!0});else B[U]=X;return B},xE=function(B){var U=HE(B,"string");return q(U)=="symbol"?U:String(U)},HE=function(B,U){if(q(B)!="object"||!B)return B;var X=B[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(B,U||"default");if(q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(U==="string"?String:Number)(B)};(function(B){var U=Object.defineProperty,X=function E(J,x){for(var H in x)U(J,H,{get:x[H],enumerable:!0,configurable:!0,set:function C(G){return x[H]=function(){return G}}})},Y={lessThanXSeconds:{one:"mens d\u2019una segonda",other:"mens de {{count}} segondas"},xSeconds:{one:"1 segonda",other:"{{count}} segondas"},halfAMinute:"30 segondas",lessThanXMinutes:{one:"mens d\u2019una minuta",other:"mens de {{count}} minutas"},xMinutes:{one:"1 minuta",other:"{{count}} minutas"},aboutXHours:{one:"environ 1 ora",other:"environ {{count}} oras"},xHours:{one:"1 ora",other:"{{count}} oras"},xDays:{one:"1 jorn",other:"{{count}} jorns"},aboutXWeeks:{one:"environ 1 setmana",other:"environ {{count}} setmanas"},xWeeks:{one:"1 setmana",other:"{{count}} setmanas"},aboutXMonths:{one:"environ 1 mes",other:"environ {{count}} meses"},xMonths:{one:"1 mes",other:"{{count}} meses"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"mai d\u2019un an",other:"mai de {{count}} ans"},almostXYears:{one:"gaireben un an",other:"gaireben {{count}} ans"}},M=function E(J,x,H){var C,G=Y[J];if(typeof G==="string")C=G;else if(x===1)C=G.one;else C=G.other.replace("{{count}}",String(x));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"d\u2019aqu\xED "+C;else return"fa "+C;return C};function N(E){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},x=J.width?String(J.width):E.defaultWidth,H=E.formats[x]||E.formats[E.defaultWidth];return H}}var $={full:"EEEE d 'de' MMMM y",long:"d 'de' MMMM y",medium:"d MMM y",short:"dd/MM/y"},D={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} 'a' {{time}}",long:"{{date}} 'a' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:D,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"eeee 'passat a' p",yesterday:"'i\xE8r a' p",today:"'u\xE8i a' p",tomorrow:"'deman a' p",nextWeek:"eeee 'a' p",other:"P"},V=function E(J,x,H,C){return L[J]};function O(E){return function(J,x){var H=x!==null&&x!==void 0&&x.context?String(x.context):"standalone",C;if(H==="formatting"&&E.formattingValues){var G=E.defaultFormattingWidth||E.defaultWidth,Z=x!==null&&x!==void 0&&x.width?String(x.width):G;C=E.formattingValues[Z]||E.formattingValues[G]}else{var I=E.defaultWidth,A=x!==null&&x!==void 0&&x.width?String(x.width):E.defaultWidth;C=E.values[A]||E.values[I]}var T=E.argumentCallback?E.argumentCallback(J):J;return C[T]}}var j={narrow:["ab. J.C.","apr. J.C."],abbreviated:["ab. J.C.","apr. J.C."],wide:["abans J\xE8sus-Crist","apr\xE8s J\xE8sus-Crist"]},f={narrow:["T1","T2","T3","T4"],abbreviated:["1\xE8r trim.","2nd trim.","3en trim.","4en trim."],wide:["1\xE8r trim\xE8stre","2nd trim\xE8stre","3en trim\xE8stre","4en trim\xE8stre"]},w={narrow:["GN","FB","M\xC7","AB","MA","JN","JL","AG","ST","OC","NV","DC"],abbreviated:["gen.","febr.","mar\xE7","abr.","mai","junh","jul.","ag.","set.","oct.","nov.","dec."],wide:["geni\xE8r","febri\xE8r","mar\xE7","abril","mai","junh","julhet","agost","setembre","oct\xF2bre","novembre","decembre"]},P={narrow:["dg.","dl.","dm.","dc.","dj.","dv.","ds."],short:["dg.","dl.","dm.","dc.","dj.","dv.","ds."],abbreviated:["dg.","dl.","dm.","dc.","dj.","dv.","ds."],wide:["dimenge","diluns","dimars","dim\xE8cres","dij\xF2us","divendres","dissabte"]},F={narrow:{am:"am",pm:"pm",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"matin",afternoon:"apr\xE8p-mi\xE8gjorn",evening:"v\xE8spre",night:"nu\xE8ch"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"matin",afternoon:"apr\xE8p-mi\xE8gjorn",evening:"v\xE8spre",night:"nu\xE8ch"},wide:{am:"a.m.",pm:"p.m.",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"matin",afternoon:"apr\xE8p-mi\xE8gjorn",evening:"v\xE8spre",night:"nu\xE8ch"}},_={narrow:{am:"am",pm:"pm",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"del matin",afternoon:"de l\u2019apr\xE8p-mi\xE8gjorn",evening:"del ser",night:"de la nu\xE8ch"},abbreviated:{am:"AM",pm:"PM",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"del matin",afternoon:"de l\u2019apr\xE8p-mi\xE8gjorn",evening:"del ser",night:"de la nu\xE8ch"},wide:{am:"ante meridiem",pm:"post meridiem",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"del matin",afternoon:"de l\u2019apr\xE8p-mi\xE8gjorn",evening:"del ser",night:"de la nu\xE8ch"}},v=function E(J,x){var H=Number(J),C=x===null||x===void 0?void 0:x.unit,G;switch(H){case 1:G="\xE8r";break;case 2:G="nd";break;default:G="en"}if(C==="year"||C==="week"||C==="hour"||C==="minute"||C==="second")G+="a";return H+G},b={ordinalNumber:v,era:O({values:j,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function E(J){return J-1}}),month:O({values:w,defaultWidth:"wide"}),day:O({values:P,defaultWidth:"wide"}),dayPeriod:O({values:F,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function Q(E){return function(J){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=x.width,C=H&&E.matchPatterns[H]||E.matchPatterns[E.defaultMatchWidth],G=J.match(C);if(!G)return null;var Z=G[0],I=H&&E.parsePatterns[H]||E.parsePatterns[E.defaultParseWidth],A=Array.isArray(I)?m(I,function(z){return z.test(Z)}):k(I,function(z){return z.test(Z)}),T;T=E.valueCallback?E.valueCallback(A):A,T=x.valueCallback?x.valueCallback(T):T;var t=J.slice(Z.length);return{value:T,rest:t}}}var k=function E(J,x){for(var H in J)if(Object.prototype.hasOwnProperty.call(J,H)&&x(J[H]))return H;return},m=function E(J,x){for(var H=0;H<J.length;H++)if(x(J[H]))return H;return};function h(E){return function(J){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=J.match(E.matchPattern);if(!H)return null;var C=H[0],G=J.match(E.parsePattern);if(!G)return null;var Z=E.valueCallback?E.valueCallback(G[0]):G[0];Z=x.valueCallback?x.valueCallback(Z):Z;var I=J.slice(C.length);return{value:Z,rest:I}}}var c=/^(\d+)(èr|nd|en)?[a]?/i,y=/\d+/i,p={narrow:/^(ab\.J\.C|apr\.J\.C|apr\.J\.-C)/i,abbreviated:/^(ab\.J\.-C|ab\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(abans Jèsus-Crist|après Jèsus-Crist)/i},d={any:[/^ab/i,/^ap/i]},g={narrow:/^T[1234]/i,abbreviated:/^[1234](èr|nd|en)? trim\.?/i,wide:/^[1234](èr|nd|en)? trimèstre/i},l={any:[/1/i,/2/i,/3/i,/4/i]},u={narrow:/^(GN|FB|MÇ|AB|MA|JN|JL|AG|ST|OC|NV|DC)/i,abbreviated:/^(gen|febr|març|abr|mai|junh|jul|ag|set|oct|nov|dec)\.?/i,wide:/^(genièr|febrièr|març|abril|mai|junh|julhet|agost|setembre|octòbre|novembre|decembre)/i},i={any:[/^g/i,/^f/i,/^ma[r?]|MÇ/i,/^ab/i,/^ma[i?]/i,/^ju[n?]|JN/i,/^ju[l?]|JL/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^d[glmcjvs]\.?/i,short:/^d[glmcjvs]\.?/i,abbreviated:/^d[glmcjvs]\.?/i,wide:/^(dimenge|diluns|dimars|dimècres|dijòus|divendres|dissabte)/i},s={narrow:[/^dg/i,/^dl/i,/^dm/i,/^dc/i,/^dj/i,/^dv/i,/^ds/i],short:[/^dg/i,/^dl/i,/^dm/i,/^dc/i,/^dj/i,/^dv/i,/^ds/i],abbreviated:[/^dg/i,/^dl/i,/^dm/i,/^dc/i,/^dj/i,/^dv/i,/^ds/i],any:[/^dg|dime/i,/^dl|dil/i,/^dm|dima/i,/^dc|dimè/i,/^dj|dij/i,/^dv|div/i,/^ds|dis/i]},o={any:/(^(a\.?m|p\.?m))|(ante meridiem|post meridiem)|((del |de la |de l’)(matin|aprèp-miègjorn|vèspre|ser|nuèch))/i},r={any:{am:/(^a)|ante meridiem/i,pm:/(^p)|post meridiem/i,midnight:/^mièj/i,noon:/^mièg/i,morning:/matin/i,afternoon:/aprèp-miègjorn/i,evening:/vèspre|ser/i,night:/nuèch/i}},e={ordinalNumber:h({matchPattern:c,parsePattern:y,valueCallback:function E(J){return parseInt(J,10)}}),era:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function E(J){return J+1}}),month:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"oc",formatDistance:M,formatLong:R,formatRelative:V,localize:b,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(B=window.dateFns)===null||B===void 0?void 0:B.locale),{},{oc:a})})})();

//# debugId=297F19448D06C32D64756e2164756e21
