import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PaymentModal } from '@/components/ui/payment-modal';
import { ReviewSystem } from '@/components/ui/review-system';
import { FileInfo } from '@/components/ui/file-download';
import { Loading, PageLoading } from '@/components/ui/loading';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { Product } from '@/types/database';
import { 
  Star, 
  Download, 
  DollarSign,
  Share2,
  Heart,
  ShoppingCart,
  User,
  Calendar,
  Package,
  Shield,
  Award,
  Eye,
  ArrowLeft
} from 'lucide-react';

const ProductDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { profile, isAuthenticated } = useAuth();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [viewCount, setViewCount] = useState(0);

  // Get referral code from URL
  const urlParams = new URLSearchParams(window.location.search);
  const referralCode = urlParams.get('ref');

  useEffect(() => {
    if (id) {
      loadProduct();
      incrementViewCount();
    }
  }, [id]);

  const loadProduct = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          creator:profiles(id, username, full_name, avatar_url, reputation_score)
        `)
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      if (!data) {
        toast({
          title: "خطأ",
          description: "المنتج غير موجود",
          variant: "destructive",
        });
        navigate('/marketplace');
        return;
      }

      setProduct(data);
      setViewCount(data.view_count || 0);
    } catch (error) {
      console.error('Error loading product:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحميل المنتج",
        variant: "destructive",
      });
      navigate('/marketplace');
    } finally {
      setLoading(false);
    }
  };

  const incrementViewCount = async () => {
    if (!id) return;

    try {
      await supabase
        .from('products')
        .update({ 
          view_count: viewCount + 1 
        })
        .eq('id', id);
    } catch (error) {
      console.error('Error incrementing view count:', error);
    }
  };

  const handleBuyProduct = () => {
    if (!product) return;
    setPaymentModalOpen(true);
  };

  const handleShare = async () => {
    const url = window.location.href;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.title,
          text: product?.description,
          url: url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      try {
        await navigator.clipboard.writeText(url);
        toast({
          title: "تم النسخ!",
          description: "تم نسخ رابط المنتج إلى الحافظة",
        });
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في نسخ الرابط",
          variant: "destructive",
        });
      }
    }
  };

  const toggleFavorite = async () => {
    if (!isAuthenticated) {
      toast({
        title: "خطأ",
        description: "يجب تسجيل الدخول أولاً",
        variant: "destructive",
      });
      return;
    }

    setIsFavorite(!isFavorite);
    // In a real implementation, you would save to favorites table
    toast({
      title: isFavorite ? "تم الإزالة" : "تم الإضافة",
      description: isFavorite ? "تم إزالة المنتج من المفضلة" : "تم إضافة المنتج للمفضلة",
    });
  };

  if (loading) {
    return <PageLoading text="جاري تحميل المنتج..." />;
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">المنتج غير موجود</h2>
              <p className="text-gray-600 mb-4">لم يتم العثور على المنتج المطلوب</p>
              <Button onClick={() => navigate('/marketplace')}>
                العودة للسوق
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const canReview = isAuthenticated && profile?.id !== product.creator_id;

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Button 
          variant="ghost" 
          onClick={() => navigate('/marketplace')}
          className="mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          العودة للسوق
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Product Image and Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Image */}
            <Card>
              <CardContent className="p-0">
                <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                  {product.cover_image_url ? (
                    <img 
                      src={product.cover_image_url} 
                      alt={product.title}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="h-24 w-24 text-gray-400" />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Product Description */}
            <Card>
              <CardHeader>
                <CardTitle>وصف المنتج</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  {product.description}
                </p>
                
                {/* Tags */}
                {product.tags && product.tags.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">العلامات:</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* File Information */}
            {product.file_url && (
              <Card>
                <CardHeader>
                  <CardTitle>معلومات الملف</CardTitle>
                </CardHeader>
                <CardContent>
                  <FileInfo
                    fileName={`${product.title}.${product.file_url.split('.').pop()}`}
                    fileType={product.category}
                  />
                </CardContent>
              </Card>
            )}

            {/* Reviews */}
            <ReviewSystem
              productId={product.id}
              averageRating={product.rating_average}
              totalReviews={product.rating_count}
              canReview={canReview}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Product Info Card */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">{product.title}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleShare}
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleFavorite}
                    >
                      <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current text-red-500' : ''}`} />
                    </Button>
                  </div>
                </div>
                <Badge variant="secondary" className="w-fit">
                  {product.category}
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Price */}
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">
                    ${product.price}
                  </div>
                  <p className="text-sm text-gray-600">سعر لمرة واحدة</p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="font-medium">
                        {product.rating_average > 0 ? product.rating_average.toFixed(1) : 'جديد'}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">
                      {product.rating_count} تقييم
                    </p>
                  </div>
                  <div>
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Download className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{product.download_count}</span>
                    </div>
                    <p className="text-xs text-gray-600">تحميل</p>
                  </div>
                </div>

                {/* Buy Button */}
                <Button 
                  onClick={handleBuyProduct}
                  className="w-full"
                  size="lg"
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  شراء الآن
                </Button>

                {referralCode && (
                  <div className="text-center p-2 bg-blue-50 rounded text-sm text-blue-700">
                    🎉 تم تطبيق رمز الإحالة - سيحصل المسوق على عمولة!
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Creator Info */}
            {product.creator && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    المنشئ
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      {product.creator.avatar_url ? (
                        <img 
                          src={product.creator.avatar_url} 
                          alt={product.creator.full_name || product.creator.username}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium">
                        {product.creator.full_name || product.creator.username}
                      </h4>
                      <div className="flex items-center gap-1">
                        <Award className="h-3 w-3 text-yellow-500" />
                        <span className="text-sm text-gray-600">
                          {product.creator.reputation_score} نقطة سمعة
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-3 w-3" />
                      <span>انضم في {new Date(product.created_at).toLocaleDateString('ar-SA')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Eye className="h-3 w-3" />
                      <span>{viewCount} مشاهدة</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Security Badge */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-green-600">
                  <Shield className="h-5 w-5" />
                  <div>
                    <p className="font-medium">دفع آمن</p>
                    <p className="text-xs text-gray-600">محمي بتقنية التشفير</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Payment Modal */}
        {product && (
          <PaymentModal
            isOpen={paymentModalOpen}
            onClose={() => setPaymentModalOpen(false)}
            product={product}
            referralCode={referralCode || undefined}
          />
        )}
      </div>
    </div>
  );
};

export default ProductDetails;
