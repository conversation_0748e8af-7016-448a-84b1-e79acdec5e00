var O=function(J){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},O(J)},D=function(J,G){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);G&&(Z=Z.filter(function(W){return Object.getOwnPropertyDescriptor(J,W).enumerable})),X.push.apply(X,Z)}return X},K=function(J){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?D(Object(X),!0).forEach(function(Z){HH(J,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):D(Object(X)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(X,Z))})}return J},HH=function(J,G,X){if(G=qH(G),G in J)Object.defineProperty(J,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[G]=X;return J},qH=function(J){var G=BH(J,"string");return O(G)=="symbol"?G:String(G)},BH=function(J,G){if(O(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(J,G||"default");if(O(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,X=function H(B,q){for(var C in q)G(B,C,{get:q[C],enumerable:!0,configurable:!0,set:function Y(U){return q[C]=function(){return U}}})},Z={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"aproximadamente 1 hora",other:"aproximadamente {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"aproximadamente 1 semana",other:"aproximadamente {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"aproximadamente 1 m\xEAs",other:"aproximadamente {{count}} meses"},xMonths:{one:"1 m\xEAs",other:"{{count}} meses"},aboutXYears:{one:"aproximadamente 1 ano",other:"aproximadamente {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},W=function H(B,q,C){var Y,U=Z[B];if(typeof U==="string")Y=U;else if(q===1)Y=U.one;else Y=U.other.replace("{{count}}",String(q));if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return"daqui a "+Y;else return"h\xE1 "+Y;return Y};function z(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},q=B.width?String(B.width):H.defaultWidth,C=H.formats[q]||H.formats[H.defaultWidth];return C}}var M={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d 'de' MMM 'de' y",short:"dd/MM/y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} '\xE0s' {{time}}",long:"{{date}} '\xE0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:z({formats:M,defaultWidth:"full"}),time:z({formats:$,defaultWidth:"full"}),dateTime:z({formats:S,defaultWidth:"full"})},R={lastWeek:function H(B){var q=B.getDay(),C=q===0||q===6?"\xFAltimo":"\xFAltima";return"'"+C+"' eeee '\xE0s' p"},yesterday:"'ontem \xE0s' p",today:"'hoje \xE0s' p",tomorrow:"'amanh\xE3 \xE0s' p",nextWeek:"eeee '\xE0s' p",other:"P"},V=function H(B,q,C,Y){var U=R[B];if(typeof U==="function")return U(q);return U};function x(H){return function(B,q){var C=q!==null&&q!==void 0&&q.context?String(q.context):"standalone",Y;if(C==="formatting"&&H.formattingValues){var U=H.defaultFormattingWidth||H.defaultWidth,E=q!==null&&q!==void 0&&q.width?String(q.width):U;Y=H.formattingValues[E]||H.formattingValues[U]}else{var I=H.defaultWidth,Q=q!==null&&q!==void 0&&q.width?String(q.width):H.defaultWidth;Y=H.values[Q]||H.values[I]}var T=H.argumentCallback?H.argumentCallback(B):B;return Y[T]}}var j={narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["antes de Cristo","depois de Cristo"]},f={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},v={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xE7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},P={narrow:["d","s","t","q","q","s","s"],short:["dom","seg","ter","qua","qui","sex","s\xE1b"],abbreviated:["dom","seg","ter","qua","qui","sex","s\xE1b"],wide:["domingo","segunda-feira","ter\xE7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xE1bado"]},_={narrow:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"noite",night:"madrugada"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"noite",night:"madrugada"},wide:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"noite",night:"madrugada"}},w={narrow:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da noite",night:"da madrugada"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da noite",night:"da madrugada"},wide:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da noite",night:"da madrugada"}},F=function H(B,q){var C=Number(B);return C+"\xBA"},m={ordinalNumber:F,era:x({values:j,defaultWidth:"wide"}),quarter:x({values:f,defaultWidth:"wide",argumentCallback:function H(B){return B-1}}),month:x({values:v,defaultWidth:"wide"}),day:x({values:P,defaultWidth:"wide"}),dayPeriod:x({values:_,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function A(H){return function(B){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=q.width,Y=C&&H.matchPatterns[C]||H.matchPatterns[H.defaultMatchWidth],U=B.match(Y);if(!U)return null;var E=U[0],I=C&&H.parsePatterns[C]||H.parsePatterns[H.defaultParseWidth],Q=Array.isArray(I)?k(I,function(N){return N.test(E)}):b(I,function(N){return N.test(E)}),T;T=H.valueCallback?H.valueCallback(Q):Q,T=q.valueCallback?q.valueCallback(T):T;var t=B.slice(E.length);return{value:T,rest:t}}}var b=function H(B,q){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&q(B[C]))return C;return},k=function H(B,q){for(var C=0;C<B.length;C++)if(q(B[C]))return C;return};function h(H){return function(B){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.match(H.matchPattern);if(!C)return null;var Y=C[0],U=B.match(H.parsePattern);if(!U)return null;var E=H.valueCallback?H.valueCallback(U[0]):U[0];E=q.valueCallback?q.valueCallback(E):E;var I=B.slice(Y.length);return{value:E,rest:I}}}var c=/^(\d+)(º|ª)?/i,y=/\d+/i,p={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|a\.?\s?e\.?\s?c\.?|d\.?\s?c\.?|e\.?\s?c\.?)/i,wide:/^(antes de cristo|antes da era comum|depois de cristo|era comum)/i},g={any:[/^ac/i,/^dc/i],wide:[/^(antes de cristo|antes da era comum)/i,/^(depois de cristo|era comum)/i]},u={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º|ª)? trimestre/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmasond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ab/i,/^mai/i,/^jun/i,/^jul/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[dstq]/i,short:/^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,wide:/^(domingo|segunda-?\s?feira|terça-?\s?feira|quarta-?\s?feira|quinta-?\s?feira|sexta-?\s?feira|s[áa]bado)/i},s={narrow:[/^d/i,/^s/i,/^t/i,/^q/i,/^q/i,/^s/i,/^s/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[áa]/i]},o={narrow:/^(a|p|meia-?\s?noite|meio-?\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i,any:/^([ap]\.?\s?m\.?|meia-?\s?noite|meio-?\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^meia/i,noon:/^meio/i,morning:/manh[ãa]/i,afternoon:/tarde/i,evening:/noite/i,night:/madrugada/i}},e={ordinalNumber:h({matchPattern:c,parsePattern:y,valueCallback:function H(B){return parseInt(B,10)}}),era:A({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:A({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function H(B){return B+1}}),month:A({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:A({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"pt",formatDistance:W,formatLong:L,formatRelative:V,localize:m,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{pt:a})})})();

//# debugId=907E87FFC78DF55764756e2164756e21
