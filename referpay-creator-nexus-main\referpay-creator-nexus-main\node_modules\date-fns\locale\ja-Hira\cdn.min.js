var A=function(X){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},A(X)},z=function(X,J){var E=Object.keys(X);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(X);J&&(H=H.filter(function(K){return Object.getOwnPropertyDescriptor(X,K).enumerable})),E.push.apply(E,H)}return E},D=function(X){for(var J=1;J<arguments.length;J++){var E=arguments[J]!=null?arguments[J]:{};J%2?z(Object(E),!0).forEach(function(H){B0(X,H,E[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(X,Object.getOwnPropertyDescriptors(E)):z(Object(E)).forEach(function(H){Object.defineProperty(X,H,Object.getOwnPropertyDescriptor(E,H))})}return X},B0=function(X,J,E){if(J=C0(J),J in X)Object.defineProperty(X,J,{value:E,enumerable:!0,configurable:!0,writable:!0});else X[J]=E;return X},C0=function(X){var J=U0(X,"string");return A(J)=="symbol"?J:String(J)},U0=function(X,J){if(A(X)!="object"||!X)return X;var E=X[Symbol.toPrimitive];if(E!==void 0){var H=E.call(X,J||"default");if(A(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(X)};(function(X){var J=Object.defineProperty,E=function C(G,U){for(var B in U)J(G,B,{get:U[B],enumerable:!0,configurable:!0,set:function Y(Z){return U[B]=function(){return Z}}})},H={lessThanXSeconds:{one:"1\u3073\u3087\u3046\u307F\u307E\u3093",other:"{{count}}\u3073\u3087\u3046\u307F\u307E\u3093",oneWithSuffix:"\u3084\u304F1\u3073\u3087\u3046",otherWithSuffix:"\u3084\u304F{{count}}\u3073\u3087\u3046"},xSeconds:{one:"1\u3073\u3087\u3046",other:"{{count}}\u3073\u3087\u3046"},halfAMinute:"30\u3073\u3087\u3046",lessThanXMinutes:{one:"1\u3077\u3093\u307F\u307E\u3093",other:"{{count}}\u3075\u3093\u307F\u307E\u3093",oneWithSuffix:"\u3084\u304F1\u3077\u3093",otherWithSuffix:"\u3084\u304F{{count}}\u3075\u3093"},xMinutes:{one:"1\u3077\u3093",other:"{{count}}\u3075\u3093"},aboutXHours:{one:"\u3084\u304F1\u3058\u304B\u3093",other:"\u3084\u304F{{count}}\u3058\u304B\u3093"},xHours:{one:"1\u3058\u304B\u3093",other:"{{count}}\u3058\u304B\u3093"},xDays:{one:"1\u306B\u3061",other:"{{count}}\u306B\u3061"},aboutXWeeks:{one:"\u3084\u304F1\u3057\u3085\u3046\u304B\u3093",other:"\u3084\u304F{{count}}\u3057\u3085\u3046\u304B\u3093"},xWeeks:{one:"1\u3057\u3085\u3046\u304B\u3093",other:"{{count}}\u3057\u3085\u3046\u304B\u3093"},aboutXMonths:{one:"\u3084\u304F1\u304B\u3052\u3064",other:"\u3084\u304F{{count}}\u304B\u3052\u3064"},xMonths:{one:"1\u304B\u3052\u3064",other:"{{count}}\u304B\u3052\u3064"},aboutXYears:{one:"\u3084\u304F1\u306D\u3093",other:"\u3084\u304F{{count}}\u306D\u3093"},xYears:{one:"1\u306D\u3093",other:"{{count}}\u306D\u3093"},overXYears:{one:"1\u306D\u3093\u3044\u3058\u3087\u3046",other:"{{count}}\u306D\u3093\u3044\u3058\u3087\u3046"},almostXYears:{one:"1\u306D\u3093\u3061\u304B\u304F",other:"{{count}}\u306D\u3093\u3061\u304B\u304F"}},K=function C(G,U,B){B=B||{};var Y,Z=H[G];if(typeof Z==="string")Y=Z;else if(U===1)if(B.addSuffix&&Z.oneWithSuffix)Y=Z.oneWithSuffix;else Y=Z.one;else if(B.addSuffix&&Z.otherWithSuffix)Y=Z.otherWithSuffix.replace("{{count}}",String(U));else Y=Z.other.replace("{{count}}",String(U));if(B.addSuffix)if(B.comparison&&B.comparison>0)return Y+"\u3042\u3068";else return Y+"\u307E\u3048";return Y};function W(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=G.width?String(G.width):C.defaultWidth,B=C.formats[U]||C.formats[C.defaultWidth];return B}}var $={full:"y\u306D\u3093M\u304C\u3064d\u306B\u3061EEEE",long:"y\u306D\u3093M\u304C\u3064d\u306B\u3061",medium:"y/MM/dd",short:"y/MM/dd"},M={full:"H\u3058mm\u3075\u3093ss\u3073\u3087\u3046 zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},R={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},S={date:W({formats:$,defaultWidth:"full"}),time:W({formats:M,defaultWidth:"full"}),dateTime:W({formats:R,defaultWidth:"full"})},L={lastWeek:"\u305B\u3093\u3057\u3085\u3046\u306Eeeee\u306Ep",yesterday:"\u304D\u306E\u3046\u306Ep",today:"\u304D\u3087\u3046\u306Ep",tomorrow:"\u3042\u3057\u305F\u306Ep",nextWeek:"\u3088\u304F\u3057\u3085\u3046\u306Eeeee\u306Ep",other:"P"},V=function C(G,U,B,Y){return L[G]};function T(C){return function(G,U){var B=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",Y;if(B==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):Z;Y=C.formattingValues[I]||C.formattingValues[Z]}else{var O=C.defaultWidth,x=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;Y=C.values[x]||C.values[O]}var Q=C.argumentCallback?C.argumentCallback(G):G;return Y[Q]}}var f={narrow:["BC","AC"],abbreviated:["\u304D\u3052\u3093\u305C\u3093","\u305B\u3044\u308C\u304D"],wide:["\u304D\u3052\u3093\u305C\u3093","\u305B\u3044\u308C\u304D"]},j={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["\u3060\u30441\u3057\u306F\u3093\u304D","\u3060\u30442\u3057\u306F\u3093\u304D","\u3060\u30443\u3057\u306F\u3093\u304D","\u3060\u30444\u3057\u306F\u3093\u304D"]},v={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1\u304C\u3064","2\u304C\u3064","3\u304C\u3064","4\u304C\u3064","5\u304C\u3064","6\u304C\u3064","7\u304C\u3064","8\u304C\u3064","9\u304C\u3064","10\u304C\u3064","11\u304C\u3064","12\u304C\u3064"],wide:["1\u304C\u3064","2\u304C\u3064","3\u304C\u3064","4\u304C\u3064","5\u304C\u3064","6\u304C\u3064","7\u304C\u3064","8\u304C\u3064","9\u304C\u3064","10\u304C\u3064","11\u304C\u3064","12\u304C\u3064"]},P={narrow:["\u306B\u3061","\u3052\u3064","\u304B","\u3059\u3044","\u3082\u304F","\u304D\u3093","\u3069"],short:["\u306B\u3061","\u3052\u3064","\u304B","\u3059\u3044","\u3082\u304F","\u304D\u3093","\u3069"],abbreviated:["\u306B\u3061","\u3052\u3064","\u304B","\u3059\u3044","\u3082\u304F","\u304D\u3093","\u3069"],wide:["\u306B\u3061\u3088\u3046\u3073","\u3052\u3064\u3088\u3046\u3073","\u304B\u3088\u3046\u3073","\u3059\u3044\u3088\u3046\u3073","\u3082\u304F\u3088\u3046\u3073","\u304D\u3093\u3088\u3046\u3073","\u3069\u3088\u3046\u3073"]},_={narrow:{am:"\u3054\u305C\u3093",pm:"\u3054\u3054",midnight:"\u3057\u3093\u3084",noon:"\u3057\u3087\u3046\u3054",morning:"\u3042\u3055",afternoon:"\u3054\u3054",evening:"\u3088\u308B",night:"\u3057\u3093\u3084"},abbreviated:{am:"\u3054\u305C\u3093",pm:"\u3054\u3054",midnight:"\u3057\u3093\u3084",noon:"\u3057\u3087\u3046\u3054",morning:"\u3042\u3055",afternoon:"\u3054\u3054",evening:"\u3088\u308B",night:"\u3057\u3093\u3084"},wide:{am:"\u3054\u305C\u3093",pm:"\u3054\u3054",midnight:"\u3057\u3093\u3084",noon:"\u3057\u3087\u3046\u3054",morning:"\u3042\u3055",afternoon:"\u3054\u3054",evening:"\u3088\u308B",night:"\u3057\u3093\u3084"}},w={narrow:{am:"\u3054\u305C\u3093",pm:"\u3054\u3054",midnight:"\u3057\u3093\u3084",noon:"\u3057\u3087\u3046\u3054",morning:"\u3042\u3055",afternoon:"\u3054\u3054",evening:"\u3088\u308B",night:"\u3057\u3093\u3084"},abbreviated:{am:"\u3054\u305C\u3093",pm:"\u3054\u3054",midnight:"\u3057\u3093\u3084",noon:"\u3057\u3087\u3046\u3054",morning:"\u3042\u3055",afternoon:"\u3054\u3054",evening:"\u3088\u308B",night:"\u3057\u3093\u3084"},wide:{am:"\u3054\u305C\u3093",pm:"\u3054\u3054",midnight:"\u3057\u3093\u3084",noon:"\u3057\u3087\u3046\u3054",morning:"\u3042\u3055",afternoon:"\u3054\u3054",evening:"\u3088\u308B",night:"\u3057\u3093\u3084"}},F=function C(G,U){var B=Number(G),Y=String(U===null||U===void 0?void 0:U.unit);switch(Y){case"year":return"".concat(B,"\u306D\u3093");case"quarter":return"\u3060\u3044".concat(B,"\u3057\u306F\u3093\u304D");case"month":return"".concat(B,"\u304C\u3064");case"week":return"\u3060\u3044".concat(B,"\u3057\u3085\u3046");case"date":return"".concat(B,"\u306B\u3061");case"hour":return"".concat(B,"\u3058");case"minute":return"".concat(B,"\u3075\u3093");case"second":return"".concat(B,"\u3073\u3087\u3046");default:return"".concat(B)}},k={ordinalNumber:F,era:T({values:f,defaultWidth:"wide"}),quarter:T({values:j,defaultWidth:"wide",argumentCallback:function C(G){return Number(G)-1}}),month:T({values:v,defaultWidth:"wide"}),day:T({values:P,defaultWidth:"wide"}),dayPeriod:T({values:_,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function q(C){return function(G){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.width,Y=B&&C.matchPatterns[B]||C.matchPatterns[C.defaultMatchWidth],Z=G.match(Y);if(!Z)return null;var I=Z[0],O=B&&C.parsePatterns[B]||C.parsePatterns[C.defaultParseWidth],x=Array.isArray(O)?b(O,function(N){return N.test(I)}):h(O,function(N){return N.test(I)}),Q;Q=C.valueCallback?C.valueCallback(x):x,Q=U.valueCallback?U.valueCallback(Q):Q;var t=G.slice(I.length);return{value:Q,rest:t}}}var h=function C(G,U){for(var B in G)if(Object.prototype.hasOwnProperty.call(G,B)&&U(G[B]))return B;return},b=function C(G,U){for(var B=0;B<G.length;B++)if(U(G[B]))return B;return};function m(C){return function(G){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=G.match(C.matchPattern);if(!B)return null;var Y=B[0],Z=G.match(C.parsePattern);if(!Z)return null;var I=C.valueCallback?C.valueCallback(Z[0]):Z[0];I=U.valueCallback?U.valueCallback(I):I;var O=G.slice(Y.length);return{value:I,rest:O}}}var c=/^だ?い?\d+(ねん|しはんき|がつ|しゅう|にち|じ|ふん|びょう)?/i,y=/\d+/i,u={narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(きげん[前後]|せいれき)/i,wide:/^(きげん[前後]|せいれき)/i},p={narrow:[/^B/i,/^A/i],any:[/^(きげんぜん)/i,/^(せいれき|きげんご)/i]},g={narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^だい[1234一二三四１２３４]しはんき/i},d={any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},l={narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])がつ/i,wide:/^([123456789]|1[012])がつ/i},i={any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},n={narrow:/^(にち|げつ|か|すい|もく|きん|ど)/,short:/^(にち|げつ|か|すい|もく|きん|ど)/,abbreviated:/^(にち|げつ|か|すい|もく|きん|ど)/,wide:/^(にち|げつ|か|すい|もく|きん|ど)ようび/},s={any:[/^にち/,/^げつ/,/^か/,/^すい/,/^もく/,/^きん/,/^ど/]},o={any:/^(AM|PM|ごぜん|ごご|しょうご|しんや|まよなか|よる|あさ)/i},r={any:{am:/^(A|ごぜん)/i,pm:/^(P|ごご)/i,midnight:/^しんや|まよなか/i,noon:/^しょうご/i,morning:/^あさ/i,afternoon:/^ごご/i,evening:/^よる/i,night:/^しんや/i}},a={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(G){return parseInt(G,10)}}),era:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"ja-Hira",formatDistance:K,formatLong:S,formatRelative:V,localize:k,match:a,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=D(D({},window.dateFns),{},{locale:D(D({},(X=window.dateFns)===null||X===void 0?void 0:X.locale),{},{jaHira:e})})})();

//# debugId=421FBF5036D309E564756e2164756e21
