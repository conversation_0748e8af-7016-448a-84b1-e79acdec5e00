var q=function(J){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(J)},D=function(J,G){var U=Object.keys(J);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(J);G&&(I=I.filter(function(x){return Object.getOwnPropertyDescriptor(J,x).enumerable})),U.push.apply(U,I)}return U},W=function(J){for(var G=1;G<arguments.length;G++){var U=arguments[G]!=null?arguments[G]:{};G%2?D(Object(U),!0).forEach(function(I){C1(J,I,U[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(U)):D(Object(U)).forEach(function(I){Object.defineProperty(J,I,Object.getOwnPropertyDescriptor(U,I))})}return J},C1=function(J,G,U){if(G=H1(G),G in J)Object.defineProperty(J,G,{value:U,enumerable:!0,configurable:!0,writable:!0});else J[G]=U;return J},H1=function(J){var G=Y1(J,"string");return q(G)=="symbol"?G:String(G)},Y1=function(J,G){if(q(J)!="object"||!J)return J;var U=J[Symbol.toPrimitive];if(U!==void 0){var I=U.call(J,G||"default");if(q(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,U=function C(Y,H){for(var B in H)G(Y,B,{get:H[B],enumerable:!0,configurable:!0,set:function X(Z){return H[B]=function(){return Z}}})},I={lessThanXSeconds:{one:"kurang dari 1 detik",other:"kurang dari {{count}} detik"},xSeconds:{one:"1 detik",other:"{{count}} detik"},halfAMinute:"setengah menit",lessThanXMinutes:{one:"kurang dari 1 menit",other:"kurang dari {{count}} menit"},xMinutes:{one:"1 menit",other:"{{count}} menit"},aboutXHours:{one:"sekitar 1 jam",other:"sekitar {{count}} jam"},xHours:{one:"1 jam",other:"{{count}} jam"},xDays:{one:"1 hari",other:"{{count}} hari"},aboutXWeeks:{one:"sekitar 1 minggu",other:"sekitar {{count}} minggu"},xWeeks:{one:"1 minggu",other:"{{count}} minggu"},aboutXMonths:{one:"sekitar 1 bulan",other:"sekitar {{count}} bulan"},xMonths:{one:"1 bulan",other:"{{count}} bulan"},aboutXYears:{one:"sekitar 1 tahun",other:"sekitar {{count}} tahun"},xYears:{one:"1 tahun",other:"{{count}} tahun"},overXYears:{one:"lebih dari 1 tahun",other:"lebih dari {{count}} tahun"},almostXYears:{one:"hampir 1 tahun",other:"hampir {{count}} tahun"}},x=function C(Y,H,B){var X,Z=I[Y];if(typeof Z==="string")X=Z;else if(H===1)X=Z.one;else X=Z.other.replace("{{count}}",H.toString());if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"dalam waktu "+X;else return X+" yang lalu";return X};function K(C){return function(){var Y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=Y.width?String(Y.width):C.defaultWidth,B=C.formats[H]||C.formats[C.defaultWidth];return B}}var S={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"d/M/yyyy"},$={full:"HH.mm.ss",long:"HH.mm.ss",medium:"HH.mm",short:"HH.mm"},M={full:"{{date}} 'pukul' {{time}}",long:"{{date}} 'pukul' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:K({formats:S,defaultWidth:"full"}),time:K({formats:$,defaultWidth:"full"}),dateTime:K({formats:M,defaultWidth:"full"})},L={lastWeek:"eeee 'lalu pukul' p",yesterday:"'Kemarin pukul' p",today:"'Hari ini pukul' p",tomorrow:"'Besok pukul' p",nextWeek:"eeee 'pukul' p",other:"P"},V=function C(Y,H,B,X){return L[Y]};function O(C){return function(Y,H){var B=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(B==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,T=H!==null&&H!==void 0&&H.width?String(H.width):Z;X=C.formattingValues[T]||C.formattingValues[Z]}else{var A=C.defaultWidth,N=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[N]||C.values[A]}var E=C.argumentCallback?C.argumentCallback(Y):Y;return X[E]}}var f={narrow:["SM","M"],abbreviated:["SM","M"],wide:["Sebelum Masehi","Masehi"]},j={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["Kuartal ke-1","Kuartal ke-2","Kuartal ke-3","Kuartal ke-4"]},w={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agt","Sep","Okt","Nov","Des"],wide:["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"]},_={narrow:["M","S","S","R","K","J","S"],short:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],abbreviated:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],wide:["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"]},v={narrow:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},wide:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"}},P={narrow:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},wide:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"}},F=function C(Y,H){var B=Number(Y);return"ke-"+B},k={ordinalNumber:F,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",argumentCallback:function C(Y){return Y-1}}),month:O({values:w,defaultWidth:"wide"}),day:O({values:_,defaultWidth:"wide"}),dayPeriod:O({values:v,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function Q(C){return function(Y){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=H.width,X=B&&C.matchPatterns[B]||C.matchPatterns[C.defaultMatchWidth],Z=Y.match(X);if(!Z)return null;var T=Z[0],A=B&&C.parsePatterns[B]||C.parsePatterns[C.defaultParseWidth],N=Array.isArray(A)?h(A,function(z){return z.test(T)}):b(A,function(z){return z.test(T)}),E;E=C.valueCallback?C.valueCallback(N):N,E=H.valueCallback?H.valueCallback(E):E;var t=Y.slice(T.length);return{value:E,rest:t}}}var b=function C(Y,H){for(var B in Y)if(Object.prototype.hasOwnProperty.call(Y,B)&&H(Y[B]))return B;return},h=function C(Y,H){for(var B=0;B<Y.length;B++)if(H(Y[B]))return B;return};function m(C){return function(Y){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=Y.match(C.matchPattern);if(!B)return null;var X=B[0],Z=Y.match(C.parsePattern);if(!Z)return null;var T=C.valueCallback?C.valueCallback(Z[0]):Z[0];T=H.valueCallback?H.valueCallback(T):T;var A=Y.slice(X.length);return{value:T,rest:A}}}var c=/^ke-(\d+)?/i,y=/\d+/i,p={narrow:/^(sm|m)/i,abbreviated:/^(s\.?\s?m\.?|s\.?\s?e\.?\s?u\.?|m\.?|e\.?\s?u\.?)/i,wide:/^(sebelum masehi|sebelum era umum|masehi|era umum)/i},g={any:[/^s/i,/^(m|e)/i]},u={narrow:/^[1234]/i,abbreviated:/^K-?\s[1234]/i,wide:/^Kuartal ke-?\s?[1234]/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|mei|jun|jul|agt|sep|okt|nov|des)/i,wide:/^(januari|februari|maret|april|mei|juni|juli|agustus|september|oktober|november|desember)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^ap/i,/^me/i,/^jun/i,/^jul/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[srkjm]/i,short:/^(min|sen|sel|rab|kam|jum|sab)/i,abbreviated:/^(min|sen|sel|rab|kam|jum|sab)/i,wide:/^(minggu|senin|selasa|rabu|kamis|jumat|sabtu)/i},s={narrow:[/^m/i,/^s/i,/^s/i,/^r/i,/^k/i,/^j/i,/^s/i],any:[/^m/i,/^sen/i,/^sel/i,/^r/i,/^k/i,/^j/i,/^sa/i]},o={narrow:/^(a|p|tengah m|tengah h|(di(\swaktu)?) (pagi|siang|sore|malam))/i,any:/^([ap]\.?\s?m\.?|tengah malam|tengah hari|(di(\swaktu)?) (pagi|siang|sore|malam))/i},r={any:{am:/^a/i,pm:/^pm/i,midnight:/^tengah m/i,noon:/^tengah h/i,morning:/pagi/i,afternoon:/siang/i,evening:/sore/i,night:/malam/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(Y){return parseInt(Y,10)}}),era:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function C(Y){return Y+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"id",formatDistance:x,formatLong:R,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=W(W({},window.dateFns),{},{locale:W(W({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{id:a})})})();

//# debugId=1C408DB7A464C32C64756e2164756e21
