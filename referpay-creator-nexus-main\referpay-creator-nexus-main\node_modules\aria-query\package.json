{"name": "aria-query", "version": "5.3.0", "description": "Programmatic access to the ARIA specification", "main": "lib/index.js", "files": ["lib"], "scripts": {"build": "rimraf lib && babel src --out-dir lib", "flow": "flow", "lint": "eslint  --config .eslintrc src __tests__ scripts", "lint:fix": "npm run lint -- --fix", "prepublishOnly": "npm run build", "pretest": "npm run lint:fix && npm run flow", "test": "npm run jest", "test:ci": "npm run jest -- --ci --runInBand", "jest": "jest --coverage __tests__/**/*", "output_as_hack": "babel-node ./scripts/output_as_hack.js"}, "repository": {"type": "git", "url": "git+https://github.com/A11yance/aria-query.git"}, "keywords": ["accessibility", "ARIA"], "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/A11yance/aria-query/issues"}, "homepage": "https://github.com/A11yance/aria-query#readme", "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.6", "@babel/eslint-parser": "^7.19.1", "@babel/node": "^7.22.5", "@babel/preset-env": "^7.19.4", "@babel/preset-flow": "^7.18.6", "babel-jest": "^29.2.1", "commander": "^9.4.1", "eslint": "^8.26.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.3", "expect": "^29.2.1", "flow-bin": "^0.191.0", "jest": "^29.2.1", "minimist": "^1.2.7", "rimraf": "^3.0.2"}, "jest": {"coverageReporters": ["lcov"], "coverageDirectory": "reports", "roots": ["<rootDir>/__tests__"]}, "browserslist": [">0.2%", "not dead", "not op_mini all", "ie 11"], "dependencies": {"dequal": "^2.0.3"}}