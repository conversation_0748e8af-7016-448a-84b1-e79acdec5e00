import { useState } from 'react';
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from './dialog';
import { Button } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { 
  CreditCard, 
  Wallet, 
  Bitcoin, 
  Loader2, 
  CheckCircle,
  ExternalLink 
} from 'lucide-react';
import { Product } from '@/types/database';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  referralCode?: string;
}

export const PaymentModal = ({ isOpen, onClose, product, referralCode }: PaymentModalProps) => {
  const { profile, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<'nowpayments' | 'binance' | 'ton'>('nowpayments');

  const paymentMethods = [
    {
      id: 'nowpayments' as const,
      name: 'NOWPayments',
      description: 'دفع بالعملات المشفرة',
      icon: Bitcoin,
      supported: ['BTC', 'ETH', 'USDT', 'BNB'],
    },
    {
      id: 'binance' as const,
      name: 'Binance Pay',
      description: 'دفع عبر Binance',
      icon: Wallet,
      supported: ['BUSD', 'USDT', 'BNB'],
    },
    {
      id: 'ton' as const,
      name: 'TON Pay',
      description: 'دفع بعملة TON',
      icon: CreditCard,
      supported: ['TON'],
    },
  ];

  const handlePayment = async () => {
    if (!isAuthenticated || !profile) {
      toast({
        title: "خطأ",
        description: "يجب تسجيل الدخول أولاً",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Create purchase record
      const purchaseData = {
        buyer_id: profile.id,
        product_id: product.id,
        amount: product.price,
        commission_amount: 3.00, // Fixed commission
        payment_method: selectedMethod,
        status: 'pending' as const,
        referral_link: referralCode ? `${window.location.origin}/?ref=${referralCode}` : null,
      };

      // Add referrer if referral code exists
      if (referralCode) {
        const { data: referralData } = await supabase
          .from('referrals')
          .select('referrer_id')
          .eq('referral_code', referralCode)
          .single();

        if (referralData) {
          purchaseData.referrer_id = referralData.referrer_id;
        }
      }

      const { data: purchase, error: purchaseError } = await supabase
        .from('purchases')
        .insert(purchaseData)
        .select()
        .single();

      if (purchaseError) throw purchaseError;

      // Generate payment URL based on selected method
      let paymentUrl = '';
      
      switch (selectedMethod) {
        case 'nowpayments':
          // Use the existing NOWPayments link with dynamic amount
          paymentUrl = `https://nowpayments.io/payment/?iid=5588486882&source=button&amount=${product.price}&currency=USD`;
          break;
        case 'binance':
          // Placeholder for Binance Pay integration
          paymentUrl = `https://pay.binance.com/checkout?amount=${product.price}&currency=USDT`;
          break;
        case 'ton':
          // Placeholder for TON Pay integration
          paymentUrl = `https://wallet.ton.org/pay?amount=${product.price}`;
          break;
      }

      // Open payment window
      const paymentWindow = window.open(
        paymentUrl,
        'payment',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      // Monitor payment window
      const checkClosed = setInterval(() => {
        if (paymentWindow?.closed) {
          clearInterval(checkClosed);
          // Check payment status
          checkPaymentStatus(purchase.id);
        }
      }, 1000);

      toast({
        title: "تم توجيهك للدفع",
        description: "يرجى إكمال عملية الدفع في النافذة الجديدة",
      });

    } catch (error: any) {
      console.error('Payment error:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء معالجة الدفع",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async (purchaseId: string) => {
    try {
      // In a real implementation, you would check with the payment provider
      // For now, we'll simulate a successful payment after a delay
      setTimeout(async () => {
        const { error } = await supabase
          .from('purchases')
          .update({ 
            status: 'completed',
            completed_at: new Date().toISOString()
          })
          .eq('id', purchaseId);

        if (!error) {
          toast({
            title: "تم الدفع بنجاح!",
            description: "يمكنك الآن تحميل المنتج من لوحة التحكم",
          });
          onClose();
        }
      }, 3000);
    } catch (error) {
      console.error('Error checking payment status:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            إتمام عملية الشراء
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Product Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">{product.title}</CardTitle>
              <CardDescription>{product.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold">${product.price}</span>
                <Badge variant="secondary">{product.category}</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <div className="space-y-3">
            <h3 className="font-medium">اختر طريقة الدفع:</h3>
            {paymentMethods.map((method) => {
              const Icon = method.icon;
              return (
                <Card 
                  key={method.id}
                  className={`cursor-pointer transition-colors ${
                    selectedMethod === method.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedMethod(method.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Icon className="h-6 w-6" />
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-gray-600">{method.description}</p>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        {method.supported.map((currency) => (
                          <Badge key={currency} variant="outline" className="text-xs">
                            {currency}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Commission Info */}
          {referralCode && (
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-700">
                    تم تطبيق رمز الإحالة - سيحصل المسوق على عمولة 33%
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Button */}
          <Button 
            onClick={handlePayment} 
            className="w-full" 
            size="lg"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                جاري المعالجة...
              </>
            ) : (
              <>
                <ExternalLink className="mr-2 h-4 w-4" />
                ادفع ${product.price}
              </>
            )}
          </Button>

          <p className="text-xs text-gray-500 text-center">
            بالنقر على "ادفع" فإنك توافق على شروط الخدمة وسياسة الخصوصية
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
